#!/usr/bin/env python3
"""
Example usage of the Trading Bot
"""

import sys
import os
from datetime import datetime, timedel<PERSON>

# Add the trading_bot package to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_bot import TradingBot

def example_backtest():
    """Example of running a backtest"""
    print("="*50)
    print("RUNNING BACKTEST EXAMPLE")
    print("="*50)

    # Create bot instance
    bot = TradingBot()

    # Show available strategies and let user choose
    available_strategies = bot.strategy_engine.get_available_strategies()
    print(f"Available strategies: {', '.join(available_strategies)}")
    strategy_name = input("Enter strategy name: ").strip()

    if strategy_name not in available_strategies:
        print(f"Strategy '{strategy_name}' not found. Please choose from available strategies.")
        return

    # Run backtest with custom parameters
    success = bot.run_backtest(
        strategy_filter=strategy_name,
        symbol="BTCUSD",
        rows_back=1000,  # Test with last 1000 1-minute candles for quick results
        initial_balance=10000.0,
        risk_per_trade=0.02
    )

    if success:
        print(f"Backtest for strategy '{strategy_name}' completed successfully!")
    else:
        print("Backtest failed!")

def example_live_trading():
    """Example of starting live trading (demo mode)"""
    print("="*50)
    print("STARTING LIVE TRADING EXAMPLE")
    print("="*50)
    print("Note: This will connect to Binance WebSocket")
    print("Press Ctrl+C to stop")

    # Create bot instance
    bot = TradingBot()

    try:
        # Start live trading
        bot.start_live_trading("BTCUSD")
    except KeyboardInterrupt:
        print("\nStopping bot...")
        bot.stop()

def example_strategy_development():
    """Example of developing a custom strategy"""
    print("="*50)
    print("CUSTOM STRATEGY DEVELOPMENT EXAMPLE")
    print("="*50)

    from trading_bot import DataManager, StrategyEngine, StrategyResult

    # Initialize components
    data_manager = DataManager()
    strategy_engine = StrategyEngine(data_manager)

    # Define a simple custom strategy
    def simple_rsi_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int):
        """Simple RSI-based strategy example"""
        try:
            df = self.data_manager.get_dataframe(name, main_tf)
            if df is None or len(df) < 14:
                return None

            # Calculate simple RSI (simplified version)
            close_prices = df['Close'].tail(14)
            gains = close_prices.diff().clip(lower=0)
            losses = (-close_prices.diff()).clip(lower=0)

            avg_gain = gains.mean()
            avg_loss = losses.mean()

            if avg_loss == 0:
                return None

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            current_price = df.iloc[-1]['Close']

            # Simple strategy: Buy when RSI < 30, Sell when RSI > 70
            if rsi < 30:  # Oversold - potential buy signal
                return StrategyResult(
                    pattern_name='simple_rsi_buy',
                    signal_strength=3.0,
                    entry_price=current_price,
                    stop_loss=current_price * 0.98,  # 2% stop loss
                    take_profit=current_price * 1.04,  # 4% take profit
                    risk_reward=2.0,
                    entry_now=True,
                    metadata={'rsi': rsi}
                )
            elif rsi > 70:  # Overbought - potential sell signal
                return StrategyResult(
                    pattern_name='simple_rsi_sell',
                    signal_strength=3.0,
                    entry_price=current_price,
                    stop_loss=current_price * 1.02,  # 2% stop loss
                    take_profit=current_price * 0.96,  # 4% take profit
                    risk_reward=2.0,
                    entry_now=True,
                    metadata={'rsi': rsi}
                )

            return None

        except Exception as e:
            print(f"Error in simple RSI strategy: {e}")
            return None

    # Register the custom strategy
    strategy_engine.strategies['simple_rsi'] = simple_rsi_strategy.__get__(strategy_engine, StrategyEngine)

    print("Custom RSI strategy registered successfully!")
    print("You can now use this strategy in backtests and live trading.")

def example_chart_generation():
    """Example of generating charts"""
    print("="*50)
    print("CHART GENERATION EXAMPLE")
    print("="*50)

    from trading_bot import DataManager, ChartGenerator

    # Initialize components
    data_manager = DataManager()
    chart_generator = ChartGenerator(data_manager)

    # Initialize some sample data
    if data_manager.initialize_data("BTCUSD"):
        print("Data initialized successfully")

        # Generate a sample chart
        level = data_manager.primary_level.get("BTCUSD", {}).get(15)
        if level:
            chart_path = chart_generator.plot_waves_and_level(
                name="BTCUSD",
                timeframe=15,
                level=level,
                num_candles=100
            )
            if chart_path:
                print(f"Chart generated: {chart_path}")
            else:
                print("Failed to generate chart")
        else:
            print("No level data available for chart generation")
    else:
        print("Failed to initialize data")

def main():
    """Main function to run examples"""
    print("Trading Bot Examples")
    print("Choose an example to run:")
    print("1. Backtest Example")
    print("2. Live Trading Example")
    print("3. Custom Strategy Development")
    print("4. Chart Generation Example")
    print("5. Exit")

    while True:
        try:
            choice = input("\nEnter your choice (1-5): ").strip()

            if choice == '1':
                example_backtest()
            elif choice == '2':
                example_live_trading()
            elif choice == '3':
                example_strategy_development()
            elif choice == '4':
                example_chart_generation()
            elif choice == '5':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1-5.")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
