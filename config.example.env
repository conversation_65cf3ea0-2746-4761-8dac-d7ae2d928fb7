# Binance Futures API Configuration
# Copy this file to .env and fill in your credentials
# DO NOT commit .env file to version control!

# Binance API Credentials
# Get these from https://testnet.binancefuture.com/ (for testnet)
# or https://www.binance.com/ (for production)
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# Trading Mode
# Set to "live" for live trading or "backtest" for backtesting
TRADING_MODE=live

# Strategy Selection
# Choose from available strategies (use --list-strategies to see all)
STRATEGY_NAME=zanshin2

# Symbol to trade
SYMBOL=BTCUSD

# Risk Management
# Percentage of balance to risk per trade (0.02 = 2%)
RISK_PER_TRADE=0.02

# Initial balance for tracking (will be synced with exchange)
INITIAL_BALANCE=10000.0

