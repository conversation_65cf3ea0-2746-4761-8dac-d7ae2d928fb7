# Telegram Bot Integration for Live Trading Notifications

This document explains how to set up and use the Telegram bot integration for receiving real-time notifications during live trading.

## Features

The Telegram integration provides notifications for:

1. **Trade Events**:
   - 🟢 Trade opened (with position chart)
   - ✅/❌ Trade closed (with P&L information)

2. **Level Changes**:
   - 🔄 Primary level changes in SCANWAVE_TIMEFRAMES (15m, 60m, 1440m)
   - 📊 Level charts attached to notifications

## Setup Instructions

### Step 1: Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a chat with Bo<PERSON><PERSON>ather and send `/newbot`
3. Follow the instructions to create your bot
4. Save the **Bot Token** (e.g., `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### Step 2: Get Your Chat ID

1. Start a chat with your newly created bot
2. Send any message to the bot
3. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Look for the `"chat":{"id":` field in the response
5. Save the **Chat ID** (e.g., `123456789`)

### Step 3: Configure Environment Variables

Set the following environment variables:

```bash
export TELEGRAM_BOT_TOKEN="your_bot_token_here"
export TELEGRAM_CHAT_ID="your_chat_id_here"
```

Or add them to your `.env` file:

```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### Step 4: Alternative Configuration

You can also set the values directly in `trading_bot/config.py`:

```python
# Telegram Bot settings
TELEGRAM_BOT_TOKEN = "your_bot_token_here"  # Replace with your actual token
TELEGRAM_CHAT_ID = "your_chat_id_here"     # Replace with your actual chat ID
```

## Usage

### Automatic Notifications (Live Mode Only)

When running the bot in live mode, notifications are sent automatically:

```python
from trading_bot import TradingBot

# Create bot in live mode
bot = TradingBot("live")

# Initialize data and start trading
bot.initialize_data("BTCUSD")
bot.enable_api_trading(leverage=10)
bot.start_live_trading("BTCUSD", strategy_name="zanshin2")

# Notifications will be sent automatically for:
# - Trade openings/closings
# - Primary level changes in 15m, 60m, 1440m timeframes
```

### Manual Testing

Test the integration manually:

```python
from trading_bot import TelegramNotifier

# Initialize notifier
notifier = TelegramNotifier()

# Test connection
if notifier.test_connection():
    print("✅ Telegram bot is working!")

# Send custom notifications
notifier.send_message("🤖 Trading bot is online!")
```

### Running the Test Suite

Use the provided test script to validate your setup:

```bash
python test_telegram_integration.py
```

## Notification Examples

### Trade Opened Notification
```
🟢 TRADE OPENED

📊 Symbol: BTCUSD
📈 Side: LONG
💰 Entry Price: 45000.0000
🛑 Stop Loss: 44000.0000
🎯 Take Profit: 47000.0000
📋 Pattern: zanshin2
📝 Reason: Strong bullish signal
⏰ Time: 2024-10-22 15:30:45
```

### Trade Closed Notification
```
✅ TRADE CLOSED

📊 Symbol: BTCUSD
📈 Side: LONG
💰 Entry Price: 45000.0000
💸 Exit Price: 46500.0000
📊 P&L: 1500.0000 (*****%)
📋 Pattern: zanshin2
⏰ Closed: 2024-10-22 16:45:30
```

### Level Change Notification
```
🔄 LEVEL CHANGE

📊 Symbol: BTCUSD
⏱️ Timeframe: 15m
📈 Level Type: Primary
⏰ Time: 2024-10-22 14:20:15
```

## Configuration Options

### In `trading_bot/config.py`:

```python
# Telegram Bot settings
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')
TELEGRAM_ENABLED = bool(TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID)
```

### Notification Triggers:

- **Trade Events**: Only in live mode (`trade_mode == "live"`)
- **Level Changes**: Only in live mode and for SCANWAVE_TIMEFRAMES (15m, 60m, 1440m)
- **Charts**: Automatically attached when available

## Troubleshooting

### Common Issues:

1. **"Telegram notifications disabled"**
   - Check that both `TELEGRAM_BOT_TOKEN` and `TELEGRAM_CHAT_ID` are set
   - Verify the token and chat ID are correct

2. **"Failed to send Telegram message"**
   - Check your internet connection
   - Verify the bot token is valid
   - Ensure the chat ID is correct
   - Make sure you've started a chat with the bot

3. **"Connection test failed"**
   - Verify the bot token format (should include the colon)
   - Check if the bot is blocked or deleted
   - Ensure the chat ID is a number (not username)

### Testing Connection:

```python
from trading_bot import TelegramNotifier

notifier = TelegramNotifier()
print(f"Enabled: {notifier.enabled}")
print(f"Token configured: {bool(notifier.bot_token)}")
print(f"Chat ID configured: {bool(notifier.chat_id)}")

if notifier.enabled:
    success = notifier.test_connection()
    print(f"Connection test: {'✅ Success' if success else '❌ Failed'}")
```

## Security Notes

- Keep your bot token secret and never commit it to version control
- Use environment variables for production deployments
- Consider using a dedicated chat/channel for trading notifications
- The bot only sends messages; it doesn't receive or process commands

## Integration Architecture

```
TradingBot
├── DataManager (with TelegramNotifier)
│   └── Sends level change notifications
├── TradeEngine (with TelegramNotifier)
│   └── Sends trade open/close notifications
└── TelegramNotifier
    ├── send_message()
    ├── send_photo()
    ├── notify_trade_opened()
    ├── notify_trade_closed()
    └── notify_level_change()
```

The integration is designed to be:
- **Non-blocking**: Notifications don't affect trading performance
- **Fail-safe**: Trading continues even if notifications fail
- **Configurable**: Easy to enable/disable via environment variables
- **Extensible**: Easy to add new notification types
