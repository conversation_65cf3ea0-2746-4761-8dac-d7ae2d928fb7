# Scanwave Mode Feature

## Overview

The `scanwave` mode is a new feature that allows you to scan and save wave and level data for a trading symbol. This is useful for:

1. **Pre-computing wave analysis** - Calculate wave patterns and levels once and reuse them
2. **Faster analysis** - Load pre-computed data instead of recalculating
3. **Data persistence** - Save analysis results for later review
4. **Debugging and testing** - Examine wave and level data at specific points in time

## How It Works

The scanwave mode performs the following steps:

1. **Load historical data** - Loads the last N rows of 1-minute candle data for the symbol
2. **Resample timeframes** - Creates higher timeframe data (5m, 15m, 1h, 1d) from 1-minute data
3. **Calculate waves** - Analyzes price action to identify wave patterns for each timeframe
4. **Calculate levels** - Determines support/resistance levels based on wave patterns
5. **Save to file** - Serializes all wave and level data to a pickle file

## Usage

### Command Line

```bash
# Basic usage - scan last 5000 rows
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 5000

# Scan more data
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 10000

# Scan different symbol
python -m trading_bot.main --mode scanwave --symbol ETHUSD --rows 5000
```

### Python API

```python
from trading_bot import TradingBot

# Create bot instance
bot = TradingBot('backtest')

# Scan waves for BTCUSD
success = bot.scan_wave(symbol='BTCUSD', rows=5000)

if success:
    print("Wave scan completed successfully!")
```

## Output

The scanwave mode creates a pickle file in the `data/` directory with the following naming convention:

```
{SYMBOL}_wave_level_{MODE}_{TIMESTAMP}.pkl
```

Example:
```
data/BTCUSD_wave_level_backtest_20241016_143022.pkl
```

### Data Structure

The saved file contains:

```python
{
    'symbol': 'BTCUSD',
    'timestamp': '2024-10-16T14:30:22.123456',
    'trade_mode': 'backtest',
    'timeframes': {
        1: {
            'waves': [...],              # List of wave dictionaries
            'primary_level': {...},      # Primary level dictionary
            'recent_level': {...},       # Recent level dictionary
            'sub_primary_level': {...},  # Sub-primary level dictionary
            'history': {...}             # Historical level data
        },
        5: {...},   # 5-minute timeframe data
        15: {...},  # 15-minute timeframe data
        60: {...},  # 1-hour timeframe data
        1440: {...} # 1-day timeframe data
    }
}
```

### Wave Structure

Each wave contains:
- `start_index`: Timestamp where wave starts
- `checked_index`: Last checked timestamp
- `peak_index`: Timestamp of wave peak (high/low)
- `confirmed`: Boolean indicating if wave is confirmed
- `label`: 1 for bullish, 0 for bearish
- `ranges`: List of range dictionaries

### Level Structure

Each level contains:
- `secondary_key_index`: Secondary key level timestamp
- `key_level_index`: Key level timestamp
- `start_index`: Start timestamp
- `checked_index`: Last checked timestamp
- `peak_index`: Peak timestamp
- `label`: 1 for bullish, 0 for bearish
- `type_wave`: 'wave' or 'trend'
- `stack_level`: Current stack level
- `start_stack_level`: Starting stack level
- Additional fields for false breaks and old peaks

## Loading Saved Data

### Command Line (via Python script)

```bash
# Run the example script with filepath
python example_scanwave.py data/BTCUSD_wave_level_backtest_20241016_143022.pkl
```

### Python API

```python
from trading_bot import TradingBot

# Create bot instance
bot = TradingBot('backtest')

# Load saved wave and level data
filepath = 'data/BTCUSD_wave_level_backtest_20241016_143022.pkl'
success = bot.load_wave_level_data(filepath)

if success:
    # Access the loaded data
    symbol = 'BTCUSD'
    timeframe = 15
    
    # Get waves
    waves = bot.data_manager.waves[symbol][timeframe]
    print(f"Total waves: {len(waves)}")
    
    # Get primary level
    primary_level = bot.data_manager.primary_level[symbol][timeframe]
    print(f"Stack level: {primary_level.get('stack_level')}")
    print(f"Type: {primary_level.get('type_wave')}")
    
    # Get last wave
    if len(waves) > 0:
        last_wave = waves[-1]
        print(f"Last wave peak: {last_wave['peak_index']}")
        print(f"Last wave confirmed: {last_wave['confirmed']}")
```

## Use Cases

### 1. Pre-compute Analysis for Backtesting

```python
# Scan waves once
bot = TradingBot('backtest')
bot.scan_wave(symbol='BTCUSD', rows=10000)

# Later, load and use for multiple backtests
bot2 = TradingBot('backtest')
bot2.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')
# Now run backtests with pre-computed wave data
```

### 2. Analyze Wave Patterns

```python
# Load saved data
bot = TradingBot('backtest')
bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')

# Analyze waves for each timeframe
for timeframe in [1, 5, 15, 60, 1440]:
    waves = bot.data_manager.waves['BTCUSD'][timeframe]
    primary_level = bot.data_manager.primary_level['BTCUSD'][timeframe]
    
    print(f"\n{timeframe}m Timeframe:")
    print(f"  Total waves: {len(waves)}")
    print(f"  Primary level stack: {primary_level.get('stack_level')}")
    
    # Count confirmed vs unconfirmed waves
    confirmed = sum(1 for w in waves if w['confirmed'])
    print(f"  Confirmed waves: {confirmed}/{len(waves)}")
```

### 3. Debug Wave Detection

```python
# Load data and examine specific waves
bot = TradingBot('backtest')
bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')

waves = bot.data_manager.waves['BTCUSD'][15]

# Examine last 5 waves
for i, wave in enumerate(waves[-5:], 1):
    print(f"\nWave {i}:")
    print(f"  Start: {wave['start_index']}")
    print(f"  Peak: {wave['peak_index']}")
    print(f"  Checked: {wave['checked_index']}")
    print(f"  Confirmed: {wave['confirmed']}")
    print(f"  Label: {'Bullish' if wave['label'] == 1 else 'Bearish'}")
```

## Performance Considerations

- **Memory usage**: Scanning large datasets (>10000 rows) may use significant memory
- **Processing time**: Depends on the number of rows and timeframes
- **File size**: Pickle files are typically 1-10 MB depending on data size
- **Recommendation**: Start with 5000 rows and increase as needed

## Example Script

See `example_scanwave.py` for a complete working example that demonstrates:
- Scanning waves and saving to file
- Loading saved wave data
- Accessing and analyzing wave and level data
- Printing summary statistics

## API Reference

### TradingBot.scan_wave()

```python
def scan_wave(self, symbol: str = "BTCUSD", rows: int = MAX_ROW_DF) -> bool:
    """
    Scan and update wave and level data for the last n rows of a symbol
    
    Args:
        symbol: Trading symbol (default: "BTCUSD")
        rows: Number of rows to scan from the tail (default: MAX_ROW_DF)
        
    Returns:
        bool: True if successful, False otherwise
    """
```

### TradingBot.load_wave_level_data()

```python
def load_wave_level_data(self, filepath: str) -> bool:
    """
    Load wave and level data from a pickle file
    
    Args:
        filepath: Path to the pickle file
        
    Returns:
        bool: True if successful, False otherwise
    """
```

### DataManager.load_wave_level_data()

```python
def load_wave_level_data(self, filepath: str) -> bool:
    """
    Load wave and level data from a pickle file
    
    Args:
        filepath: Path to the pickle file
        
    Returns:
        bool: True if successful, False otherwise
    """
```

## Troubleshooting

### Issue: "File not found" error
**Solution**: Make sure the filepath is correct and the file exists in the `data/` directory

### Issue: "No data available" warning
**Solution**: Ensure the CSV data files exist in the `data/` directory before running scanwave

### Issue: Out of memory error
**Solution**: Reduce the number of rows to scan (e.g., use 5000 instead of 10000)

### Issue: Pickle load error
**Solution**: The file may be corrupted. Re-run the scanwave mode to generate a new file

## Future Enhancements

Potential improvements for the scanwave feature:
- Support for JSON export format
- Compression for smaller file sizes
- Incremental updates (append new data to existing file)
- Wave pattern statistics and summary reports
- Integration with chart generation

