{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3ffa5aaa", "metadata": {}, "outputs": [], "source": ["    %load_ext autoreload\n", "    %autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "id": "d1f1ba79", "metadata": {}, "outputs": [], "source": ["import logging\n", "import time\n", "import signal\n", "import sys\n", "from datetime import datetime\n", "from typing import Dict\n", "\n", "from trading_bot.config import *\n", "from trading_bot.data_manager import DataManager\n", "from trading_bot.strategy_engine import StrategyEngine\n", "from trading_bot.chart_generator import ChartGenerator\n", "from trading_bot.binance_client import BinanceWebSocketClient, BinanceDataFetcher, create_binance_hook\n", "from trading_bot.trade_engine import TradeEngine\n", "from trading_bot.main import TradingBot"]}, {"cell_type": "code", "execution_count": 3, "id": "3ed0f680", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-03 22:51:39,091 - trading_bot.main - INFO - Initializing data for BTCUSD\n", "2025-08-03 22:51:52,293 - trading_bot.main - INFO - Data initialization completed\n"]}], "source": ["bot = TradingBot()\n", "name = 'BTCUSD'\n", "main_tf = 15\n", "high_tf = 60\n", "very_high_tf = 1440\n", "\n", "# Initialize data\n", "if not bot.initialize_data('BTCUSD'):\n", "    print(\"Failed to initialize data\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a65ab55e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "current_time = bot.data_manager.waves[name][main_tf][-1]['checked_index']\n", "\n", "# Update data to next timeframe\n", "next_time = current_time + pd.Timedelta(minutes=main_tf)\n", "bot.data_manager.update_data_to_new_timestamp(name, next_time, main_tf)\n", "current_time = next_time\n", "\n", "# Plot level\n", "bot.chart_generator.plot_position_analysis(name, main_tf, None, True)"]}, {"cell_type": "code", "execution_count": null, "id": "a0bd4c4e", "metadata": {}, "outputs": [], "source": ["current_time"]}, {"cell_type": "code", "execution_count": null, "id": "e9d0c93c", "metadata": {}, "outputs": [], "source": ["\n", "bot.data_manager.primary_level[name][main_tf]"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}