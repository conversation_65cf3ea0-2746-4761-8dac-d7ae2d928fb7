{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Trading Bot Module",
      "type": "debugpy",
      "request": "launch",
      "module": "trading_bot.main",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "args": ["--mode", "backtest", "--symbol", "BTCUSD", "--strategy", "correction_broken"],
      "justMyCode": false,
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "PYTHONDONTWRITEBYTECODE": "1"
      },
      "python": "${command:python.interpreterPath}",
      "stopOnEntry": false,
      "subProcess": true
    },
    {
      "name": "Run scanwave from avaiable data",
      "type": "debugpy",
      "request": "launch",
      "module": "trading_bot.main",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "args": ["--mode", "scanwave", "--symbol", "BTCUSD"],
      "justMyCode": false,
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "PYTHONDONTWRITEBYTECODE": "1"
      },
      "python": "${command:python.interpreterPath}",
      "stopOnEntry": false,
      "subProcess": true
    },
    {
      "name": "Python Debugger: Test Chart Debug SC",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/debug_sc.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "PYTHONDONTWRITEBYTECODE": "1"
      },
      "python": "${command:python.interpreterPath}",
      "stopOnEntry": false,
      "subProcess": true
    },
    {
      "name": "Python Debugger: Trading Bot Live Mode",
      "type": "debugpy",
      "request": "launch",
      "module": "trading_bot.main",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "args": ["--mode", "live", "--symbol", "BTCUSD", "--strategy", "correction_broken"],
      "justMyCode": false,
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "PYTHONDONTWRITEBYTECODE": "1"
      },
      "python": "${command:python.interpreterPath}",
      "stopOnEntry": false,
      "subProcess": true
    }
  ]
}
