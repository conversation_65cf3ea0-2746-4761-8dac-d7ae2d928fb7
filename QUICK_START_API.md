# Quick Start Guide - Binance Futures API Trading

## 5-Minute Setup

### Step 1: Get Testnet API Keys (2 minutes)

1. Go to https://testnet.binancefuture.com/
2. Sign in with GitHub or register
3. Click your profile → API Management
4. Create new API key
5. Copy API Key and Secret Key

### Step 2: Configure Environment (1 minute)

```bash
# Set environment variables
export BINANCE_API_KEY="your_api_key_here"
export BINANCE_API_SECRET="your_secret_key_here"
```

Or create `.env` file:
```bash
cp config.example.env .env
# Edit .env with your keys
```

### Step 3: Update Config (1 minute)

Edit `trading_bot/config.py`:

```python
# Enable API trading
BINANCE_FUTURES_TESTNET = True  # Using testnet
BINANCE_FUTURES_ENABLED = True  # Enable API
DRY_RUN_MODE = True  # Start with dry run for safety

# Risk settings (adjust as needed)
DEFAULT_LEVERAGE = 5  # Start with low leverage
MAX_POSITION_SIZE_USDT = 100  # Small positions for testing
MAX_DAILY_LOSS_USDT = 50  # Conservative daily limit
```

### Step 4: Run Bot (1 minute)

```bash
# Start with dry run mode first
python -m trading_bot.main --mode live --symbol BTCUSD --strategy zanshin2
```

Or use Python:

```python
from trading_bot import TradingBot

bot = TradingBot(trade_mode="live")
bot.initialize_data(symbol="BTCUSD")
bot.enable_api_trading(leverage=5)
bot.start_live_trading(symbol="BTCUSD", strategy_name="zanshin2")
```

## Testing Progression

### Phase 1: Dry Run (1-3 days)
```python
# config.py
DRY_RUN_MODE = True
BINANCE_FUTURES_ENABLED = True
```
- Bot logs signals but doesn't execute
- Verify signals are correct
- Check logs for any errors

### Phase 2: Small Real Orders (1 week)
```python
# config.py
DRY_RUN_MODE = False
MAX_POSITION_SIZE_USDT = 50
DEFAULT_LEVERAGE = 3
```
- Execute small real orders
- Monitor closely
- Verify SL/TP work correctly

### Phase 3: Normal Trading (ongoing)
```python
# config.py
DRY_RUN_MODE = False
MAX_POSITION_SIZE_USDT = 500
DEFAULT_LEVERAGE = 10
```
- Increase position sizes gradually
- Continue monitoring
- Adjust based on performance

## Common Commands

### Check Status
```python
status = bot.get_live_trading_status()
print(f"Balance: {status['balance']}")
print(f"Open trades: {status['open_trades']}")
```

### Monitor Orders
```python
order_status = bot.monitor_orders(symbol="BTCUSD")
print(f"Open orders: {order_status['open_orders_count']}")
```

### Sync with Exchange
```python
bot.sync_with_exchange(symbol="BTCUSD")
```

### Emergency Stop
```python
bot.emergency_close_all(symbol="BTCUSD")
```

### Disable API Trading
```python
bot.disable_api_trading()
```

## Available Strategies

List all strategies:
```bash
python -m trading_bot.main --list-strategies
```

Popular strategies:
- `zanshin2` - Trend following with confirmation
- `bos` - Break of structure
- `correction_broken` - Correction breakout
- `smc` - Smart money concepts
- `confluent_key` - Multiple confluence

## Configuration Quick Reference

| Parameter | Description | Recommended Start |
|-----------|-------------|-------------------|
| `BINANCE_FUTURES_TESTNET` | Use testnet | `True` |
| `BINANCE_FUTURES_ENABLED` | Enable API | `True` |
| `DRY_RUN_MODE` | Simulate orders | `True` initially |
| `DEFAULT_LEVERAGE` | Leverage multiplier | `3-5` |
| `MAX_POSITION_SIZE_USDT` | Max position | `50-100` |
| `MIN_POSITION_SIZE_USDT` | Min position | `10` |
| `MAX_DAILY_LOSS_USDT` | Daily loss limit | `50-100` |

## Safety Checklist

Before enabling real trading:

- [ ] Tested on testnet with dry run mode
- [ ] Verified signals are correct
- [ ] Tested for at least 3 days
- [ ] Set conservative position limits
- [ ] Set daily loss limit
- [ ] Understand emergency procedures
- [ ] Have monitoring plan
- [ ] Reviewed all documentation

## Troubleshooting

### "API credentials not found"
```bash
# Set environment variables
export BINANCE_API_KEY="your_key"
export BINANCE_API_SECRET="your_secret"
```

### "Failed to validate API credentials"
- Check API key is correct
- Verify Futures permission is enabled
- Ensure using testnet keys with `BINANCE_FUTURES_TESTNET = True`

### "Position size below minimum"
- Increase `risk_per_trade` in bot settings
- Or decrease `MIN_POSITION_SIZE_USDT`

### Orders not executing
- Check `BINANCE_FUTURES_ENABLED = True`
- Verify `DRY_RUN_MODE = False` for real orders
- Call `bot.enable_api_trading()` after initialization

## Example Scripts

### Basic Live Trading
```python
from trading_bot import TradingBot

bot = TradingBot(trade_mode="live")
bot.initialize_data(symbol="BTCUSD")
bot.enable_api_trading(leverage=5)
bot.start_live_trading(symbol="BTCUSD", strategy_name="zanshin2")
```

### With Monitoring
```python
import time
from trading_bot import TradingBot

bot = TradingBot(trade_mode="live")
bot.initialize_data(symbol="BTCUSD")
bot.enable_api_trading(leverage=5)

# Start in background thread
import threading
thread = threading.Thread(
    target=bot.start_live_trading,
    args=("BTCUSD", "zanshin2")
)
thread.start()

# Monitor every 30 seconds
while True:
    status = bot.get_live_trading_status()
    print(f"Balance: {status['balance']}, Open: {status['open_trades']}")
    time.sleep(30)
```

### Command Line
```bash
# Dry run mode
python -m trading_bot.main --mode live --symbol BTCUSD --strategy zanshin2

# List strategies
python -m trading_bot.main --list-strategies

# Run example script
python example_live_trading.py

# Monitor mode
python example_live_trading.py monitor

# Emergency close
python example_live_trading.py emergency
```

## Important Notes

⚠️ **Always start with testnet**
⚠️ **Use dry run mode first**
⚠️ **Start with low leverage (3-5x)**
⚠️ **Set conservative position limits**
⚠️ **Monitor regularly**
⚠️ **Never invest more than you can afford to lose**

## Next Steps

1. ✅ Complete 5-minute setup above
2. ✅ Run in dry run mode for 1-3 days
3. ✅ Enable small real orders for 1 week
4. ✅ Gradually increase position sizes
5. ✅ Monitor and optimize

## Full Documentation

- **Complete Setup**: See [BINANCE_FUTURES_SETUP.md](BINANCE_FUTURES_SETUP.md)
- **Implementation Details**: See [IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)
- **General Usage**: See [README.md](README.md)

## Support

If you encounter issues:
1. Check logs in `logs/trading_bot.log`
2. Review [BINANCE_FUTURES_SETUP.md](BINANCE_FUTURES_SETUP.md) troubleshooting section
3. Verify configuration in `config.py`
4. Test with dry run mode
5. Check Binance API status

---

**Ready to start?** Follow the 5-minute setup above! 🚀

