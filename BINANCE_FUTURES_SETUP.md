# Binance Futures API Integration Guide

This guide will help you set up and use the Binance Futures API integration for live trading with the trading bot.

## Table of Contents
1. [Setting Up Binance Futures Testnet](#setting-up-binance-futures-testnet)
2. [Configuration](#configuration)
3. [Safety Features](#safety-features)
4. [Usage Examples](#usage-examples)
5. [Troubleshooting](#troubleshooting)

---

## Setting Up Binance Futures Testnet

### Step 1: Create a Testnet Account

1. Visit the Binance Futures Testnet: https://testnet.binancefuture.com/
2. Click on "Register" or use your GitHub account to sign in
3. Once logged in, you'll receive test USDT in your account (usually 10,000 USDT)

### Step 2: Generate API Keys

1. Go to your testnet account settings
2. Navigate to "API Management"
3. Click "Create API" and follow the instructions
4. **Important**: Save your API Key and Secret Key securely
5. Enable "Futures" trading permissions for the API key

### Step 3: Configure the Bot

You have two options to configure your API credentials:

#### Option A: Environment Variables (Recommended)
```bash
export BINANCE_API_KEY="your_api_key_here"
export BINANCE_API_SECRET="your_api_secret_here"
```

#### Option B: Update config.py
Edit `trading_bot/config.py`:
```python
BINANCE_FUTURES_API_KEY = "your_api_key_here"
BINANCE_FUTURES_API_SECRET = "your_api_secret_here"
```

---

## Configuration

### Key Configuration Parameters

Edit `trading_bot/config.py` to customize these settings:

```python
# Binance Futures API settings
BINANCE_FUTURES_TESTNET = True  # Set to False for production trading
BINANCE_FUTURES_API_KEY = ""  # Your API key
BINANCE_FUTURES_API_SECRET = ""  # Your API secret
BINANCE_FUTURES_ENABLED = False  # Enable/disable actual order execution

# Trading parameters
DEFAULT_LEVERAGE = 10  # Default leverage for futures trading (1-125)
MAX_POSITION_SIZE_USDT = 1000  # Maximum position size in USDT
MIN_POSITION_SIZE_USDT = 10  # Minimum position size in USDT
MAX_DAILY_LOSS_USDT = 500  # Maximum daily loss before stopping trading
DRY_RUN_MODE = True  # If True, simulate orders without executing them
```

### Configuration Explanation

- **BINANCE_FUTURES_TESTNET**: Use testnet for testing, production for real trading
- **BINANCE_FUTURES_ENABLED**: Master switch to enable/disable API trading
- **DEFAULT_LEVERAGE**: Leverage multiplier (higher = more risk)
- **MAX_POSITION_SIZE_USDT**: Prevents opening positions larger than this value
- **MIN_POSITION_SIZE_USDT**: Prevents opening positions smaller than this value
- **MAX_DAILY_LOSS_USDT**: Bot stops trading if daily loss exceeds this amount
- **DRY_RUN_MODE**: Test mode that logs orders without executing them

---

## Safety Features

### 1. Dry Run Mode
When `DRY_RUN_MODE = True`, the bot will:
- Log all trading signals
- Simulate order execution
- NOT place real orders on the exchange
- Perfect for testing strategies

### 2. Daily Loss Limit
- Bot tracks daily profit/loss
- Automatically stops trading if `MAX_DAILY_LOSS_USDT` is exceeded
- Resets at midnight UTC

### 3. Position Size Limits
- Enforces minimum and maximum position sizes
- Prevents over-leveraging
- Calculates position size based on risk management

### 4. Rate Limiting
- Automatically limits API requests to stay within Binance limits
- Prevents API ban due to excessive requests
- Implements 100ms minimum interval between requests

### 5. Emergency Stop Function
```python
bot.emergency_close_all(symbol="BTCUSD")
```
This will:
- Cancel all open orders
- Close all open positions
- Disable API trading
- Use only in emergency situations

---

## Usage Examples

### Example 1: Basic Live Trading Setup

```python
from trading_bot import TradingBot

# Create bot in live mode
bot = TradingBot(trade_mode="live")

# Initialize data
bot.initialize_data(symbol="BTCUSD")

# Enable API trading with 10x leverage
bot.enable_api_trading(leverage=10)

# Start live trading with a specific strategy
bot.start_live_trading(symbol="BTCUSD", strategy_name="zanshin2")
```

### Example 2: Testing with Dry Run Mode

```python
# In config.py, set:
# DRY_RUN_MODE = True
# BINANCE_FUTURES_ENABLED = True

from trading_bot import TradingBot

bot = TradingBot(trade_mode="live")
bot.initialize_data(symbol="BTCUSD")
bot.enable_api_trading(leverage=5)
bot.start_live_trading(symbol="BTCUSD", strategy_name="bos")

# Bot will log all signals but won't execute real orders
```

### Example 3: Monitoring and Management

```python
from trading_bot import TradingBot

bot = TradingBot(trade_mode="live")
bot.initialize_data(symbol="BTCUSD")
bot.enable_api_trading(leverage=10)

# Get trading status
status = bot.get_live_trading_status()
print(f"Balance: {status['balance']}")
print(f"Open trades: {status['open_trades']}")

# Sync with exchange
bot.sync_with_exchange(symbol="BTCUSD")

# Monitor orders
order_status = bot.monitor_orders(symbol="BTCUSD")
print(f"Open orders: {order_status['open_orders_count']}")

# Disable API trading (keeps bot running but stops new orders)
bot.disable_api_trading()

# Emergency close all positions
bot.emergency_close_all(symbol="BTCUSD")
```

### Example 4: Command Line Usage

```bash
# Start live trading with testnet
python -m trading_bot.main --mode live --symbol BTCUSD --strategy zanshin2

# The bot will:
# 1. Load historical data
# 2. Connect to Binance WebSocket for real-time data
# 3. Initialize Binance Futures API client
# 4. Wait for trading signals
# 5. Execute orders when signals are detected (if enabled)
```

### Example 5: Gradual Deployment

```python
# Step 1: Test with dry run mode
# config.py: DRY_RUN_MODE = True, BINANCE_FUTURES_ENABLED = True
bot = TradingBot(trade_mode="live")
bot.start_live_trading(symbol="BTCUSD", strategy_name="zanshin2")
# Monitor for a few days, verify signals are correct

# Step 2: Enable real trading with small position sizes
# config.py: DRY_RUN_MODE = False, MAX_POSITION_SIZE_USDT = 50
bot = TradingBot(trade_mode="live")
bot.enable_api_trading(leverage=5)  # Start with low leverage
bot.start_live_trading(symbol="BTCUSD", strategy_name="zanshin2")
# Monitor closely for a week

# Step 3: Increase position sizes gradually
# config.py: MAX_POSITION_SIZE_USDT = 200
# Continue monitoring and adjusting
```

---

## Troubleshooting

### Issue: "API credentials not found"
**Solution**: 
- Set environment variables: `BINANCE_API_KEY` and `BINANCE_API_SECRET`
- Or update `config.py` with your credentials

### Issue: "Failed to validate API credentials"
**Solution**:
- Verify your API key and secret are correct
- Check that API key has Futures trading permissions enabled
- Ensure you're using testnet keys with `BINANCE_FUTURES_TESTNET = True`

### Issue: "Position size below minimum"
**Solution**:
- Increase your `risk_per_trade` parameter
- Increase your account balance
- Adjust `MIN_POSITION_SIZE_USDT` in config

### Issue: "Daily loss limit exceeded"
**Solution**:
- This is a safety feature working correctly
- Review your strategy performance
- Adjust `MAX_DAILY_LOSS_USDT` if needed
- Wait until next day for automatic reset

### Issue: "Rate limit reached"
**Solution**:
- The bot automatically handles rate limiting
- If you see this frequently, reduce trading frequency
- Check for multiple bot instances running

### Issue: Orders not executing
**Solution**:
- Check `BINANCE_FUTURES_ENABLED = True` in config
- Verify `DRY_RUN_MODE = False` for real trading
- Call `bot.enable_api_trading()` after initialization
- Check logs for specific error messages

---

## Best Practices

1. **Always start with testnet**: Test thoroughly before using real funds
2. **Use dry run mode first**: Verify signals are correct before enabling real trading
3. **Start with low leverage**: Begin with 2-5x leverage, increase gradually
4. **Set conservative limits**: Use strict position size and daily loss limits
5. **Monitor regularly**: Check bot status and logs frequently
6. **Keep API keys secure**: Never commit API keys to version control
7. **Use environment variables**: Preferred method for API credentials
8. **Test emergency procedures**: Practice using emergency_close_all()
9. **Maintain sufficient balance**: Ensure account has enough margin
10. **Review closed trades**: Analyze performance regularly

---

## Production Deployment Checklist

Before deploying to production:

- [ ] Tested extensively on testnet
- [ ] Verified all strategies perform as expected
- [ ] Set appropriate position size limits
- [ ] Set appropriate daily loss limits
- [ ] Configured leverage conservatively
- [ ] Set up monitoring and alerts
- [ ] Tested emergency stop procedures
- [ ] Secured API keys properly
- [ ] Updated `BINANCE_FUTURES_TESTNET = False`
- [ ] Set `DRY_RUN_MODE = False`
- [ ] Set `BINANCE_FUTURES_ENABLED = True`
- [ ] Documented your configuration
- [ ] Set up logging and monitoring
- [ ] Have a plan for handling errors

---

## Support and Resources

- Binance Futures Testnet: https://testnet.binancefuture.com/
- Binance API Documentation: https://binance-docs.github.io/apidocs/futures/en/
- Bot Documentation: See README.md
- Issues: Report on GitHub

---

## Disclaimer

**IMPORTANT**: Trading cryptocurrencies involves substantial risk of loss. This bot is provided as-is without any guarantees. Always:
- Test thoroughly on testnet first
- Start with small amounts
- Never invest more than you can afford to lose
- Monitor your bot regularly
- Understand the risks of leveraged trading

The developers are not responsible for any financial losses incurred while using this software.

