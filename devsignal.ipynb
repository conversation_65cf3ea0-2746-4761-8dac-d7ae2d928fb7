{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "def update_level_and_status_to_row_number(name, timeframe, row_number):\n", "    global df, waves, recent_level, primary_level, trend_status, old_trend_status, old_label, old_stack_level, sub_primary_level\n", "    if row_number is None:\n", "        return\n", "\n", "    waves[name][timeframe] = []\n", "    trend_status[name][timeframe] = []\n", "    old_trend_status[name][timeframe] = []\n", "\n", "    # Init level start first candle and check to whole df\n", "    if df[name][timeframe].iloc[0]['Open'] <= df[name][timeframe].iloc[0]['Close']:\n", "        label = 1.0\n", "    else:\n", "        label = 0.0\n", "\n", "    first_row = df[name][timeframe].iloc[0]\n", "    df[name][timeframe].loc[first_row.name, 'ma_candle'] = df[name][timeframe].loc[first_row.name, 'candle_size']\n", "\n", "    recent_level[name][timeframe] = {\n", "        'secondary_key_index': first_row.name,\n", "        'key_level_index': first_row.name,\n", "        'start_index': first_row.name,\n", "        'checked_index': first_row.name,\n", "        'peak_index': first_row.name,\n", "        'label': label,\n", "        'type_wave': 'wave',\n", "        'stack_level': 0,\n", "        'start_stack_level': 0,\n", "        'false_break_level_index': None,\n", "        'false_break_peak_index': None,\n", "        'old_peak_index': None,\n", "        'old_key_level_index': None,\n", "    }\n", "    primary_level[name][timeframe] = copy.deepcopy(recent_level[name][timeframe])\n", "    sub_primary_level[name][timeframe] = copy.deepcopy(recent_level[name][timeframe])\n", "    latest_wave = {\n", "        'start_index': recent_level[name][timeframe]['start_index'],\n", "        'checked_index': recent_level[name][timeframe]['checked_index'],\n", "        'peak_index': recent_level[name][timeframe]['peak_index'],\n", "        'confirmed': True,\n", "        'label': recent_level[name][timeframe]['label'],\n", "        'ranges': [],\n", "    }\n", "    waves[name][timeframe].append(latest_wave)\n", "    # old_trend_status[name][timeframe].append(copy.deepcopy(primary_level[name][timeframe]))\n", "    # trend_status[name][timeframe].append(copy.deepcopy(primary_level[name][timeframe]))\n", "\n", "    new_index = 0\n", "    row = df[name][timeframe].iloc[new_index]\n", "    update_trend_status(df[name][timeframe], waves[name][timeframe], primary_level[name][timeframe], name, timeframe)\n", "    while new_index < row_number:\n", "        update_level_and_trend_status_with_new_row(name, timeframe, row)\n", "        index = df[name][timeframe].index.get_loc(row.name)\n", "        print(f\"--Index row {new_index} -- {name} {timeframe}\")\n", "        new_index = index + 1\n", "        row = df[name][timeframe].iloc[new_index]\n", "    return df[name][timeframe].iloc[new_index - 1]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import mplfinance as mpf\n", "import numpy as np\n", "from datetime import datetime\n", "\n", "# df = pd.read_csv('eu_15.csv', index_col='Time', parse_dates=True)\n", "# df['candle_size'] = abs(df['Open'] - df['Close'])\n", "# df['ma_candle'] = None\n", "history = {\n", "    'primary_level': [],\n", "    'recent_level': [],\n", "    'sub_primary_level': [],\n", "}\n", "recent_level = {}\n", "primary_level = {}\n", "sub_primary_level = {}\n", "waves = {}\n", "past_ranges = {}\n", "primary_waves = {}\n", "position_status = {}\n", "pending_position = {}\n", "closed_position = {}\n", "cancelled_position = {}\n", "trend_status = {}\n", "old_trend_status = {}\n", "number_cal_ma_candle = 49\n", "minimum_imbalance_ratio = 1\n", "big_candle_ratio = 2.2\n", "eg_candle_ratio = 80.0\n", "doji_candle_ratio = 11.0\n", "number_candles_sideway = 5\n", "min_rr = 1\n", "min_point_open = 4\n", "ratio_ma_tp = 1\n", "ratio_ma_sl = 3\n", "small_candle_ratio = 1/3\n", "percent_default_tp = 0.5\n", "min_rr_open_now = 2.5\n", "fake_breakout_checking = False\n", "break_index = None\n", "main_trend_reverse_checking = False\n", "space_break_ratio = 0.1 # For update waves\n", "level_break_space_ratio = 0.5\n", "max_confirm_break_ratio = 11\n", "weak_trend_ratio = 0.2\n", "liquidity_ratio = 1\n", "# For stra\n", "waiting_stack_level_confirm = False\n", "is_trendline_broken = False\n", "broken_trendline = None\n", "# BOS stra\n", "bos_signal = False\n", "lowest_point = None\n", "bos_index = None\n", "bos_checked_index = None\n", "# trend reverse\n", "old_label = {}\n", "old_stack_level = {}\n", "\n", "# Max row df\n", "max_row_df = 100000\n", "# Init data\n", "\n", "df = {}\n", "timeframes = [1, 5, 15, 60, 1440]\n", "check_symbols = {\n", "    'BTCUSD': None,\n", "    # 'XAUUSD': 41,\n", "}\n", "for name, id in check_symbols.items():\n", "    df[name] = {}\n", "    recent_level[name] = {}\n", "    primary_level[name] = {}\n", "    sub_primary_level[name] = {}\n", "    trend_status[name] = {}\n", "    position_status[name] = {}\n", "    pending_position[name] = {}\n", "    old_trend_status[name] = {}\n", "    closed_position[name] = {}\n", "    cancelled_position[name] = {}\n", "    waves[name] = {}\n", "    past_ranges[name] = {}\n", "    old_label[name] = {}\n", "    old_stack_level[name] = {}\n", "\n", "    # Handle timefram 1 minute\n", "\n", "    for timeframe in timeframes:\n", "\n", "        if timeframe == 1:\n", "            file_name = f\"data/{name}_{timeframe}.csv\"\n", "            df[name][timeframe] = pd.read_csv(file_name, sep=',', parse_dates=['Open time', 'Close time'], index_col='Open time')\n", "\n", "            df[name][timeframe] = df[name][timeframe].tail(max_row_df)\n", "        else:\n", "            df[name][timeframe] = df[name][1].resample(f'{timeframe}min').agg({\n", "                'Open': 'first',\n", "                'High': 'max',\n", "                'Low': 'min',\n", "                'Close': 'last',\n", "                'Volume': 'sum',\n", "            })\n", "\n", "        df[name][timeframe]['candle_size'] = abs(df[name][timeframe]['Open'] - df[name][timeframe]['Close'])\n", "        df[name][timeframe]['Labels'] = (df[name][timeframe]['Close'] > df[name][timeframe]['Open']).astype(int)\n", "        df[name][timeframe]['label'] = df[name][timeframe]['Labels']\n", "        df[name][timeframe]['ma_candle'] = None\n", "        df[name][timeframe]['span_a'] = float('nan')\n", "        df[name][timeframe]['span_b'] = float('nan')\n", "        df[name][timeframe]['tenkan'] = float('nan')\n", "        df[name][timeframe]['kijun '] = float('nan')\n", "        df[name][timeframe]['chikou'] = float('nan')\n", "        recent_level[name][timeframe] = None\n", "        primary_level[name][timeframe] = None\n", "        sub_primary_level[name][timeframe] = None\n", "        waves[name][timeframe] = []\n", "        past_ranges[name][timeframe] = []\n", "        trend_status[name][timeframe] = []\n", "        old_trend_status[name][timeframe] = []\n", "        old_label[name][timeframe] = []\n", "        old_stack_level[name][timeframe] = []\n", "        position_status[name][timeframe] = []\n", "        pending_position[name][timeframe] = []\n", "        closed_position[name][timeframe] = []\n", "        cancelled_position[name][timeframe] = []\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"Open time,Open,High,Low,Close,Volume,Close time,Quote asset volume,Number of trades,Taker buy base asset volume,Taker buy quote asset volume,Ignore\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[name][1440].head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def is_any_candle_outside(check_candles, label):\n", "    first_candle = check_candles.iloc[0]\n", "    other_candles = check_candles.iloc[1:]\n", "    if label == 0: # if wave down\n", "        return (other_candles['High'] < first_candle['Low']).any()\n", "    else: # wave up\n", "        return (other_candles['Low'] > first_candle['High']).any()\n", "\n", "def is_candle_complete_inside(first_candle, check_candle):\n", "    return check_candle['High'] <= first_candle['High'] and check_candle['Low'] >= first_candle['Low']\n", "\n", "def is_break_with_space(checked_candles, label, break_ratio=None):\n", "    global space_break_ratio\n", "    break_ratio = break_ratio or space_break_ratio\n", "    if label == 1:\n", "        direction = 1\n", "        peak = 'High'\n", "    else:\n", "        direction = -1\n", "        peak = 'Low'\n", "    high = checked_candles.iloc[0][peak]\n", "    higher_space = None\n", "    for local_index, row in checked_candles.iloc[1:].iterrows():\n", "        if higher_space is not None:\n", "            if (row['Close'] - high)*direction > row['ma_candle']*break_ratio:\n", "                return True\n", "            else:\n", "                high = higher_space\n", "                higher_space = None\n", "        if (row['Close'] - high)*direction > 0:\n", "            higher_space = row['Close']\n", "    return False\n", "\n", "def is_eg_candle(row):\n", "    return abs(row['Open']-row['Close'])*100.0/abs(row['High']-row['Low']) >= eg_candle_ratio and size_candle(row) > row['ma_candle']\n", "\n", "def is_doji_candle(row):\n", "    return abs(row['Open']-row['Close'])*100.0/abs(row['High']-row['Low']) <= doji_candle_ratio\n", "\n", "def size_candle(row):\n", "    return abs(row['High'] - row['Low'])\n", "\n", "def check_imbalance_3_candles(first_candle, second_candle, third_candle, label):\n", "    if is_eg_candle(second_candle) == False or is_big_candle(second_candle) == False:\n", "        return False\n", "    if label == 0:\n", "        if third_candle['Close'] > third_candle['Open'] or second_candle['Close'] > second_candle['Open']:\n", "            return False\n", "        if first_candle['Low'] <= third_candle['High']:\n", "            return False\n", "    else: # key_level['label'] == 1\n", "        if third_candle['Close'] < third_candle['Open'] or second_candle['Close'] < second_candle['Open']:\n", "            return False\n", "        if first_candle['High'] >= third_candle['Low']:\n", "            return False\n", "    return first_candle.name\n", "\n", "def has_imbalance(df, start_index, end_index, label):\n", "    i = df.index.get_loc(start_index)\n", "    peak_i = df.index.get_loc(end_index)\n", "    if len(df.iloc[i:peak_i+1]) < 3:\n", "        return False\n", "    while i + 2 <= peak_i:\n", "        first_candle = df.iloc[i]\n", "        second_candle = df.iloc[i+1]\n", "        third_candle = df.iloc[i+2]\n", "        i = i + 1\n", "        if second_candle['candle_size'] < second_candle['ma_candle'] * minimum_imbalance_ratio:\n", "            continue\n", "        if label == 0:\n", "            if third_candle['Close'] > third_candle['Open'] or second_candle['Close'] > second_candle['Open']:\n", "                continue\n", "            if first_candle['Low'] <= third_candle['High']:\n", "                continue\n", "        else: # key_level['label'] == 1\n", "            if third_candle['Close'] < third_candle['Open'] or second_candle['Close'] < second_candle['Open']:\n", "                continue\n", "            if first_candle['High'] >= third_candle['Low']:\n", "                continue\n", "        return first_candle.name\n", "    return False\n", "\n", "def is_trend_has_imbalance(df, trend_key):\n", "    return has_imbalance(df, trend_key['key_level_index'], trend_key['peak_index'], trend_key['label'])\n", "\n", "def has_big_candle(start_index, end_index):\n", "    i = start_index\n", "    peak_i = end_index\n", "    for _, candle in df.iloc[i:peak_i+1].iterrows():\n", "        if is_eg_candle(candle) and candle['candle_size'] >= candle['ma_candle']*big_candle_ratio:\n", "            return True\n", "    return False\n", "\n", "def is_big_candle(candle):\n", "    return candle['candle_size'] >= candle['ma_candle']*big_candle_ratio\n", "\n", "def mean_candle(candle):\n", "    return (candle['Open'] + candle['Close'])/2.0\n", "\n", "def is_confirm_break(df, row_index, price, label, start_index, is_primary=True):\n", "    global max_confirm_break_ratio, level_break_space_ratio\n", "    break_candle_index = find_break_candle(df, row_index, price, label, False, start_index)\n", "    if break_candle_index is None or break_candle_index >= row_index:\n", "        return False\n", "    if label == 1:\n", "        low = 'Low'\n", "        high = 'High'\n", "        direction = 1\n", "    else:\n", "        low = 'High'\n", "        high = 'Low'\n", "        direction = -1\n", "\n", "    # Price going too far so obvious break\n", "    if (df.loc[break_candle_index]['Close'] - price) > max_confirm_break_ratio * df.loc[break_candle_index]['ma_candle']:\n", "        print('Break confirmed bypass react')\n", "        return True\n", "\n", "    next_candle_index = df.iloc[df.index.get_loc(break_candle_index)+1].name\n", "    if next_candle_index > row_index:\n", "        return False\n", "    try:\n", "        next_next_candle_index = df.iloc[df.index.get_loc(next_candle_index)+1].name\n", "    except:\n", "        next_next_candle_index = None\n", "    else:\n", "        if next_next_candle_index > row_index:\n", "            next_next_candle_index = None\n", "    # if break candle is EG candle and the next candle size is smaller or equal to 1/3 break candle\n", "    if is_eg_candle(df.loc[break_candle_index]) and size_candle(df.loc[next_candle_index])/size_candle(df.loc[break_candle_index]) <= small_candle_ratio \\\n", "        and (df.loc[next_candle_index][low] - price)*direction > 0:\n", "        print('Break with EG candle and small confirm')\n", "        return True\n", "    if is_eg_candle(df.loc[break_candle_index]) and is_eg_candle(df.loc[next_candle_index]) and df.loc[next_candle_index]['Labels'] == label\\\n", "        and (df.loc[next_candle_index][low] - price)*direction > 0:\n", "        print('Break with 2 EG candle')\n", "        return True\n", "    if is_big_candle(df.loc[break_candle_index]) and (df.loc[next_candle_index]['Close'] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle'] and size_candle(df.loc[next_candle_index])/size_candle(df.loc[break_candle_index]) <= small_candle_ratio:\n", "        print('Break with big candle and second candle close above and small')\n", "        return True\n", "    if is_break_with_space(df.loc[break_candle_index:row_index], label, level_break_space_ratio):\n", "        print('Break with space')\n", "        return True\n", "    if  next_next_candle_index and (df.loc[next_candle_index][low] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle'] and (df.loc[next_next_candle_index][low] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle']:\n", "        print('Break with candle count')\n", "        return True\n", "    fake_price_zone = df.loc[break_candle_index][high]\n", "    return is_confirm_break(df, row_index, fake_price_zone, label, next_candle_index, is_primary)\n", "\n", "\n", "def find_break_candle(df, check_index, price, label, last=True, start_index=None):\n", "    if last:\n", "        if label == 1:\n", "            open = 'Open'\n", "            close = 'Close'\n", "            peak = 'High'\n", "            direction = -1\n", "            max_high = df.loc[df.iloc[0].name:check_index]['High'].max()\n", "            if price > max_high:\n", "                return None\n", "        else:\n", "            open = 'Close'\n", "            close = 'Open'\n", "            peak = 'Low'\n", "            direction = 1\n", "            min_low = df.loc[df.iloc[0].name:check_index]['Low'].min()\n", "            if price < min_low:\n", "                return None\n", "        while True:\n", "            if price >= df.loc[check_index][open] and price <= df.loc[check_index][close] or (df.loc[check_index][peak] - price)*direction > 0:\n", "                return check_index\n", "            if df.index.get_loc(check_index) == 0:\n", "                return None\n", "            check_index = df.iloc[df.index.get_loc(check_index) - 1].name\n", "            if check_index < df.iloc[0].name:\n", "                return None\n", "    else:\n", "        if start_index is None:\n", "            print('Need start index to find first break candle')\n", "            raise ValueError('Need start index to find break candle')\n", "\n", "        if label == 1:\n", "            open = 'Open'\n", "            close = 'Close'\n", "            peak = 'Low'\n", "            direction = 1\n", "            max_high = df.loc[start_index:check_index]['Close'].max()\n", "            if price > max_high:\n", "                return None\n", "            else:\n", "                local_df = df.loc[start_index:check_index]\n", "                local_df = local_df.loc[local_df['Close'] > price]\n", "                return local_df.iloc[0].name if len(local_df) > 0 else None\n", "        else:\n", "            open = 'Close'\n", "            close = 'Open'\n", "            peak = 'High'\n", "            direction = -1\n", "            min_low = df.loc[start_index:check_index]['Close'].min()\n", "            if price < min_low:\n", "                return None\n", "            else:\n", "                local_df = df.loc[start_index:check_index]\n", "                local_df = local_df.loc[local_df['Close'] < price]\n", "                return local_df.iloc[0].name if len(local_df) > 0 else None\n", "\n", "def count_waves_from_peak_index(check_index, waves, peak_label, count_unconfirm=False):\n", "    for index, wave in enumerate(reversed(waves)):\n", "        if check_index >= wave['start_index'] and check_index <= wave['peak_index']:\n", "            result = index\n", "            if waves[-1]['confirmed'] is False and count_unconfirm == False:\n", "                result = result - 1\n", "            if peak_label != wave['label']:\n", "                result = result + 1\n", "            if result == -1:\n", "                return 0\n", "            return result\n", "    return 0\n", "\n", "\n", "\n", "def close_out_body_candle(candle, row_check):\n", "    return row_check['Close'] > candle['Open'] and row_check['Close'] > candle['Close'] or \\\n", "        row_check['Close'] < candle['Open'] and row_check['Close'] < candle['Close']\n", "\n", "def last_candle_label_same_wave(df, wave):\n", "    filtered_df = df.loc[wave['start_index']:wave['checked_index']].loc[df['label'] == wave['label']]\n", "    if len(filtered_df) > 0:\n", "        return filtered_df.iloc[-1]\n", "    return df.loc[wave['start_index']]\n", "\n", "def update_waves_with_new_row_df(df, waves, new_row_df):\n", "    index = df.index.get_loc(new_row_df.name)\n", "    print(f\"New Index call update waves: {index}\")\n", "    previous_index = df.index.get_loc(waves[-1]['checked_index'])\n", "    if index <= previous_index:\n", "        print('This row df already updated')\n", "        return recent_level\n", "    # Update indicator ichimoku\n", "    last_26 = max(index - 26, 0)\n", "    # Calculate Tenkan-sen (Conversion Line): (9-period high + 9-period low)/2\n", "    df.loc[new_row_df.name, 'tenkan'] = (df.loc[:new_row_df.name].tail(9)['High'].max() + df.loc[:new_row_df.name].tail(9)['Low'].min())/2.0\n", "\n", "    # Cal<PERSON>-sen (Base Line): (26-period high + 26-period low)/2\n", "    df.loc[new_row_df.name, 'kijun'] = (df.loc[:new_row_df.name].tail(26)['High'].max() + df.loc[:new_row_df.name].tail(26)['Low'].min())/2.0\n", "\n", "    # Calculate Chikou Span (Lagging Span): Current closing price plotted 26 periods back\n", "    df.loc[df.iloc[last_26].name, 'chikou'] = df.loc[new_row_df.name]['Close']\n", "\n", "    # Calculate Senkou Span A (Leading Span A): (<PERSON>kan-sen + <PERSON><PERSON>-sen)/2 plotted 26 periods ahead\n", "    # For current calculations, we need to use current Tenkan and <PERSON>jun values\n", "    df.loc[new_row_df.name, 'span_a'] = (df.loc[new_row_df.name]['tenkan'] + df.loc[new_row_df.name]['kijun'])/2.0\n", "\n", "    # Calculate Senkou Span B (Leading Span B): (52-period high + 52-period low)/2 plotted 26 periods ahead\n", "    # Use current 52-period data, not data from 26 periods ago\n", "    df.loc[new_row_df.name, 'span_b'] = (df.loc[:new_row_df.name].tail(52)['High'].max() + df.loc[:new_row_df.name].tail(52)['Low'].min())/2.0\n", "\n", "    df.loc[new_row_df.name, 'candle_size'] = abs(df.loc[new_row_df.name]['Open'] - df.loc[new_row_df.name]['Close'])\n", "    # df.loc[new_row_df.name, 'effort'] = df.loc[new_row_df.name, 'Volume']/df.loc[new_row_df.name, 'candle_size']\n", "    # Calculate ma candle size\n", "    if index-number_cal_ma_candle < 0:\n", "        df.loc[new_row_df.name, 'ma_candle'] = df.loc[new_row_df.name, 'candle_size']\n", "    else:\n", "        df.loc[new_row_df.name, 'ma_candle'] = df.iloc[index-number_cal_ma_candle:index+1]['candle_size'].sum()/number_cal_ma_candle\n", "\n", "    # Check if new candle going same direction with curent wave or close in body previous candle\n", "    direction = 1 if waves[-1]['label'] else -1\n", "    if (new_row_df['Close'] - df.loc[waves[-1]['checked_index'], 'Close'])*direction >= 0 or close_out_body_candle(last_candle_label_same_wave(df, waves[-1]), new_row_df) == False:\n", "        print(f\"New row df going same direction with current wave label is {new_row_df['Labels']} or close in body previous\")\n", "        # Extend wave\n", "        waves[-1]['checked_index'] = new_row_df.name\n", "        # Check new peak current wave\n", "        if waves[-1]['label'] == 1:\n", "            if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:\n", "                waves[-1]['peak_index'] = new_row_df.name\n", "        else:\n", "            if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:\n", "                waves[-1]['peak_index'] = new_row_df.name\n", "\n", "        # Check confirm\n", "        if waves[-1]['confirmed'] == False:\n", "            # Check if have at least two candles and not inside opposite type bar\n", "            if waves[-1]['peak_index'] > waves[-2]['peak_index'] and is_any_candle_outside(df.loc[waves[-2]['peak_index']:waves[-1]['checked_index']], waves[-1]['label']):\n", "                # Confirm\n", "                waves[-1]['confirmed'] = True\n", "                print('Current wave now just confirm')\n", "                waves[-1]['ranges'].append({\n", "                    'start_index': waves[-1]['start_index'],\n", "                    'end_index': df.iloc[previous_index].name,\n", "                    'count': index - df.index.get_loc(waves[-1]['start_index'])\n", "                })\n", "                waves[-1]['start_index'] = waves[-2]['peak_index']\n", "                if waves[-1]['label']:\n", "                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['High'].idxmax()\n", "                else:\n", "                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['Low'].idxmin()\n", "                waves[-2]['checked_index'] = waves[-2]['peak_index']\n", "            else:\n", "                # Not Confirm\n", "                print('Current wave still not confirm, waiting for new row')\n", "                if waves[-2]['label'] == 1:\n", "                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:\n", "                        waves[-2]['peak_index'] = new_row_df.name\n", "                        waves[-2]['checked_index'] = new_row_df.name\n", "                        waves[-1]['start_index'] = new_row_df.name\n", "                else:\n", "                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:\n", "                        waves[-2]['peak_index'] = new_row_df.name\n", "                        waves[-2]['checked_index'] = new_row_df.name\n", "                        waves[-1]['start_index'] = new_row_df.name\n", "    else: # Different direction with current wave\n", "        print('New row direction different with current wave')\n", "        # Check current wave confirm?\n", "\n", "        if waves[-1]['confirmed']:\n", "            if waves[-1]['label'] == 1:\n", "                if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:\n", "                    waves[-1]['peak_index'] = new_row_df.name\n", "                    waves[-1]['checked_index'] = new_row_df.name\n", "            else:\n", "                if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:\n", "                    waves[-1]['peak_index'] = new_row_df.name\n", "                    waves[-1]['checked_index'] = new_row_df.name\n", "\n", "            print('Make new unconfirmed wave append')\n", "            # Spawn new waves with confirm false\n", "            latest_wave = {\n", "                'start_index': new_row_df.name,\n", "                'checked_index': new_row_df.name,\n", "                'peak_index': new_row_df.name,\n", "                'confirmed': <PERSON><PERSON><PERSON>,\n", "                'label': 1 - waves[-1]['label'],\n", "                'ranges': [],\n", "            }\n", "            # if waves[-1]['label'] == 1:\n", "            #     if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:\n", "            #         waves[-1]['peak_index'] = new_row_df.name\n", "            # else:\n", "            #     if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:\n", "            #         waves[-1]['peak_index'] = new_row_df.name\n", "            waves.append(latest_wave)\n", "        else: # Current wave not confirm\n", "            print('Current wave was not confirmed then new row different direction occurs')\n", "            # Check if false confirm reverse\n", "            if is_false_confirm_reverse(df, waves, new_row_df):\n", "                print('False confirm reverse break, so extend previous wave to current wave + new row df')\n", "                # Extend previous wave to current wave and new_row_df\n", "                # Check new peak current wave again\n", "                waves[-2]['peak_index'] = new_row_df.name\n", "                waves[-2]['checked_index'] = new_row_df.name\n", "                waves[-2]['ranges'].append({\n", "                    'start_index': waves[-1]['start_index'],\n", "                    'end_index': df.iloc[previous_index].name,\n", "                    'count': index - df.index.get_loc(waves[-1]['start_index'])\n", "                })\n", "                waves.pop()\n", "                if len(waves) > 1:\n", "                    if waves[-1]['label']:\n", "                        id_min = df.loc[waves[-1]['start_index']:new_row_df.name, 'Low'].idxmin()\n", "                        if df.loc[waves[-2]['peak_index']]['Low'] > df.loc[id_min]['Low']:\n", "                            waves[-2]['peak_index'] = id_min\n", "                            waves[-2]['checked_index'] = id_min\n", "                            waves[-1]['start_index'] = id_min\n", "                    else:\n", "                        id_max = df.loc[waves[-1]['start_index']:new_row_df.name, 'High'].idxmax()\n", "                        if df.loc[waves[-2]['peak_index']]['High'] < df.loc[id_max]['High']:\n", "                            waves[-2]['peak_index'] = id_max\n", "                            waves[-2]['checked_index'] = id_max\n", "                            waves[-1]['start_index'] = id_max\n", "\n", "            else:\n", "                # Check new peak current wave again\n", "                if waves[-1]['label'] == 1:\n", "                    if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:\n", "                        waves[-1]['peak_index'] = new_row_df.name\n", "                else:\n", "                    if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:\n", "                        waves[-1]['peak_index'] = new_row_df.name\n", "\n", "                if waves[-2]['label'] == 1:\n", "                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:\n", "                        waves[-2]['peak_index'] = new_row_df.name\n", "                else:\n", "                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:\n", "                        waves[-2]['peak_index'] = new_row_df.name\n", "\n", "            waves[-1]['checked_index'] = new_row_df.name\n", "\n", "def is_false_confirm_reverse(df, waves, new_row):\n", "    if len(waves) < 2:\n", "        return False\n", "    if waves[-2]['label']:\n", "        direction = 1\n", "        peak = 'High'\n", "    else:\n", "        direction = -1\n", "        peak = 'Low'\n", "    return (new_row['Close'] - df.loc[waves[-2]['peak_index'], peak])*direction > 0\n", "\n", "def find_smc_ob(df, key_level):\n", "    # Find candles in 68 ratio\n", "    if key_level['label']:\n", "        high = 'High'\n", "        low = 'Low'\n", "        direction = 1\n", "    else:\n", "        high = 'Low'\n", "        low = 'High'\n", "        direction = -1\n", "    range_price = abs(df.loc[key_level['peak_index'], high] - df.loc[key_level['key_level_index'], low])\n", "    max_price = df.loc[key_level['peak_index'], high] - range_price*0.618*direction\n", "    min_price = df.loc[key_level['peak_index'], high] - range_price*0.8*direction\n", "    candles = df.loc[key_level['key_level_index']:key_level['peak_index']]\n", "    ob_list = []\n", "    # Get first candle potienal to be ob\n", "    for i, candle in candles.iterrows():\n", "        if (candle['Low'] >= min_price and candle['Low'] <= max_price) or (candle['High'] >= min_price and candle['High'] <= max_price) or (candle['Close'] >= min_price and candle['Close'] <= max_price):\n", "            for ob in ob_list:\n", "                # Check new ob test old ob\n", "                if (candle[low] - df.loc[ob['ob_index']][high])*direction < 0:\n", "                    ob_list.remove(ob)\n", "            if is_doji_candle(candle) or candle['label'] != key_level['label']:\n", "                df_candles = candles.loc[candle.name:].iloc[1:]\n", "                end_index = df_candles.loc[df_candles['label'] != key_level['label']]\n", "                if len(end_index) > 0:\n", "                    end_index = end_index.iloc[0].name\n", "                else:\n", "                    end_index = None\n", "                df_candles = df.loc[candle.name:end_index].iloc[0:3]\n", "                if has_imbalance(df, df_candles.iloc[0].name, df_candles.iloc[-1].name, key_level['label']):\n", "                    ob_list.append({\n", "                        'ob_index': candle.name,\n", "                        'strength': 'strong' if is_eg_candle(candle) else 'weak',\n", "                    })\n", "                    continue\n", "    return ob_list\n", "\n", "def level_from_waves(df, waves, primary_key=False, init_level=None):\n", "    if len(waves) == 0:\n", "        raise ValueError(\"waves is empty\")\n", "    index = 1\n", "    if init_level:\n", "        recent_level = init_level\n", "        if recent_level['peak_index'] < waves[0]['peak_index']:\n", "            recent_level['peak_index'] = waves[0]['peak_index']\n", "        if recent_level['checked_index'] < waves[0]['checked_index']:\n", "            recent_level['checked_index'] = waves[0]['checked_index']\n", "        # filter checked_index\n", "        if waves[-1]['checked_index'] > recent_level['checked_index'] and len(waves) > 4:\n", "            index_latest_wave = find_wave_index_candle_belongs_to(recent_level['checked_index'], waves)\n", "            if index_latest_wave and index_latest_wave > 1 - len(waves):\n", "                index = max(index_latest_wave + len(waves) - 1, 1)\n", "    else:\n", "        recent_level = {\n", "            'secondary_key_index': waves[0]['start_index'],\n", "            'key_level_index': waves[0]['start_index'],\n", "            'start_index': waves[0]['start_index'],\n", "            'checked_index': waves[0]['checked_index'],\n", "            'peak_index': waves[0]['peak_index'],\n", "            'label': waves[0]['label'],\n", "            'type_wave': 'wave',\n", "            'stack_level': 0,\n", "            'start_stack_level': 0,\n", "            'false_break_level_index': None,\n", "            'false_break_peak_index': None,\n", "            'old_peak_index': None,\n", "            'old_key_level_index': None,\n", "        }\n", "    while index < len(waves):\n", "        if waves[index]['confirmed'] == False:\n", "            recent_level['checked_index'] = waves[index]['checked_index']\n", "            return recent_level\n", "        if recent_level['label'] == 1:\n", "            peak = 'Low'\n", "            opposite_peak = 'High'\n", "            direction = 1\n", "            compare_func = min\n", "        else:\n", "            peak = 'High'\n", "            opposite_peak = 'Low'\n", "            direction = -1\n", "            compare_func = max\n", "        if waves[index]['label'] != recent_level['label']:\n", "            if recent_level.get('ob_correction_index') is None:\n", "                recent_level['ob_correction_index'] = waves[index]['peak_index']\n", "                recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)\n", "            else:\n", "                if (df.loc[waves[index]['peak_index'], peak] - df.loc[recent_level['ob_correction_index'], peak])*direction < 0:\n", "                    recent_level['ob_correction_index'] = waves[index]['peak_index']\n", "                    recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)\n", "            price_check = df.loc[recent_level['false_break_level_index']][peak] if recent_level['false_break_level_index'] else df.loc[recent_level['key_level_index']][peak]\n", "\n", "            lookback_index = max(0, df.index.get_loc(waves[index]['checked_index']) - 26)\n", "            displaced_span_b = df.iloc[lookback_index]['span_b']\n", "            price_check = compare_func(price_check, displaced_span_b)\n", "            expanded_checked_index = waves[index+1]['checked_index'] if index < len(waves)-1 else waves[index]['checked_index']\n", "            # Compare with current key level\n", "            if (price_check - df.loc[waves[index]['peak_index']][peak])*direction > 0:\n", "                if waves[index]['confirmed'] and recent_level['key_level_index'] < waves[index]['start_index'] and ((df.loc[recent_level['key_level_index']][peak] - df.loc[waves[index]['start_index']]['Close'])*direction > 0 or is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'], recent_level['false_break_level_index'] or recent_level['key_level_index'], primary_key)):\n", "                    number_waves = count_waves_from_peak_index(recent_level['peak_index'], waves[0:index+1], recent_level['label'])\n", "                    if number_waves < 3:\n", "                        recent_level = {\n", "                            'secondary_key_index': recent_level['key_level_index'],\n", "                            'key_level_index': recent_level['peak_index'],\n", "                            'start_index': recent_level['key_level_index'],\n", "                            'checked_index': waves[index]['checked_index'],\n", "                            'peak_index': waves[index]['peak_index'],\n", "                            'label': waves[index]['label'],\n", "                            'type_wave': 'wave',\n", "                            'stack_level': 0,\n", "                            'start_stack_level': 0,\n", "                            'false_break_level_index': None,\n", "                            'false_break_peak_index': None,\n", "                            'old_peak_index': None,\n", "                            'old_key_level_index': None,\n", "                            'old_stack_level': recent_level['stack_level'],\n", "                        }\n", "                    else:\n", "                        if primary_key:\n", "                            if recent_level['label']:\n", "                                start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmax()\n", "                            else:\n", "                                start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmin()\n", "                            new_start_wave_index = find_wave_index_candle_belongs_to(start_index, waves[0:index+1])\n", "                            if waves[new_start_wave_index]['label'] == recent_level['label']:\n", "                                new_start_wave_index = new_start_wave_index + 1\n", "                            init_level = {\n", "                                'secondary_key_index': recent_level['key_level_index'],\n", "                                'key_level_index': start_index,\n", "                                'start_index': start_index,\n", "                                'checked_index': waves[new_start_wave_index]['checked_index'],\n", "                                'peak_index': waves[new_start_wave_index]['peak_index'],\n", "                                'label': waves[new_start_wave_index]['label'],\n", "                                'type_wave': 'wave',\n", "                                'stack_level': 0,\n", "                                'start_stack_level': 0,\n", "                                'false_break_level_index': None,\n", "                                'false_break_peak_index': None,\n", "                                'old_peak_index': None,\n", "                                'old_key_level_index': None,\n", "                                'old_stack_level': recent_level['stack_level'],\n", "                            }\n", "                            if len(waves[new_start_wave_index:index+1]) < 2:\n", "                                recent_level = init_level\n", "                            else:\n", "                                recent_level = level_from_waves(df, waves[new_start_wave_index:index+1], True, init_level)\n", "                                recent_level['start_stack_level'] = recent_level['stack_level']\n", "                        else:\n", "                            recent_level = {\n", "                                'secondary_key_index': waves[index-2]['peak_index'],\n", "                                'key_level_index': waves[index-1]['peak_index'],\n", "                                'start_index': waves[index-2]['peak_index'],\n", "                                'checked_index': waves[index]['checked_index'],\n", "                                'peak_index': waves[index]['peak_index'],\n", "                                'label': waves[index]['label'],\n", "                                'type_wave': 'trend',\n", "                                'stack_level': 1,\n", "                                'start_stack_level': 1,\n", "                                'false_break_level_index': None,\n", "                                'false_break_peak_index': None,\n", "                                'old_peak_index': None,\n", "                                'old_key_level_index': None,\n", "                                'old_stack_level': recent_level['stack_level'],\n", "                        }\n", "                else:\n", "                    if index < len(waves) - 1:\n", "                        recent_level['false_break_level_index'] = waves[index]['peak_index']\n", "                    recent_level['checked_index'] = waves[index]['checked_index']\n", "            else:\n", "                recent_level['checked_index'] = waves[index]['checked_index']\n", "        else:\n", "            # Compare with key level peak index\n", "            # ratio_trend = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)\n", "            # and ratio_trend < -0.25\n", "\n", "            price_check = df.loc[recent_level['false_break_peak_index']][opposite_peak] if recent_level['false_break_peak_index'] else df.loc[recent_level['peak_index']][opposite_peak]\n", "            expanded_checked_index = waves[index+1]['checked_index'] if index < len(waves)-1 else waves[index]['checked_index']\n", "            if (df.loc[waves[index]['peak_index']][opposite_peak] - price_check)*direction > 0:\n", "                if waves[index]['confirmed'] and recent_level['peak_index'] < waves[index]['start_index'] and ((df.loc[waves[index]['start_index']]['Close'] - df.loc[recent_level['peak_index']][opposite_peak])*direction > 0 or is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'], recent_level['false_break_peak_index'] or recent_level['peak_index'], primary_key)):\n", "                    if primary_key:\n", "                        if recent_level['label']:\n", "                            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']][peak].idxmin()\n", "                        else:\n", "                            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']][opposite_peak].idxmax()\n", "                        recent_level = {\n", "                            'secondary_key_index': recent_level['peak_index'],\n", "                            'key_level_index': key_level,\n", "                            'start_index': recent_level['start_index'],\n", "                            'checked_index': waves[index]['checked_index'],\n", "                            'peak_index': waves[index]['peak_index'],\n", "                            'label': waves[index]['label'],\n", "                            'type_wave': 'trend',\n", "                            'confirmed': True,\n", "                            'stack_level': recent_level['stack_level'] + 1,\n", "                            'start_stack_level': recent_level['start_stack_level'],\n", "                            'false_break_level_index': None,\n", "                            'false_break_peak_index': None,\n", "                            'old_peak_index': recent_level['peak_index'],\n", "                            'old_key_level_index': recent_level['key_level_index'],\n", "                            'old_stack_level': recent_level.get('old_stack_level'),\n", "                        }\n", "                    else:\n", "                        recent_level = {\n", "                            'secondary_key_index': waves[index-2]['peak_index'],\n", "                            'key_level_index': waves[index-1]['peak_index'],\n", "                            'start_index': recent_level['start_index'],\n", "                            'checked_index': waves[index]['checked_index'],\n", "                            'peak_index': waves[index]['peak_index'],\n", "                            'label': waves[index]['label'],\n", "                            'type_wave': 'trend',\n", "                            'confirmed': True,\n", "                            'stack_level': recent_level['stack_level'] + 1,\n", "                            'start_stack_level': recent_level['start_stack_level'],\n", "                            'false_break_level_index': None,\n", "                            'false_break_peak_index': None,\n", "                            'old_peak_index': recent_level['peak_index'],\n", "                            'old_key_level_index': recent_level['key_level_index'],\n", "                            'old_stack_level': recent_level.get('old_stack_level'),\n", "                        }\n", "                else:\n", "                    if index < len(waves) - 1:\n", "                        recent_level['false_break_peak_index'] = waves[index]['peak_index']\n", "                    recent_level['checked_index'] = waves[index]['checked_index']\n", "            else:\n", "                recent_level['checked_index'] = waves[index]['checked_index']\n", "        index = index + 1\n", "    return recent_level\n", "\n", "def opposite_level_from_key_level(df, waves, key_level, is_primary):\n", "    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)\n", "    if number_waves == 0:\n", "        return None\n", "    if number_waves == 1:\n", "        return level_from_waves(df, [waves[-1]], is_primary)\n", "    return level_from_waves(df, waves[-number_waves:], is_primary)\n", "\n", "def is_wave_testing_ob(df, wave, ob_index, label_key_level):\n", "    if label_key_level == 1:\n", "        return df.loc[wave['peak_index']]['Low'] <= df.loc[ob_index]['High'] and \\\n", "            is_confirm_break(df, wave['peak_index'], df.loc[ob_index]['Low'], wave['label']) == False\n", "    else:\n", "        return df.loc[wave['peak_index']]['High'] >= df.loc[ob_index]['Low'] and \\\n", "            is_confirm_break(df, wave['peak_index'], df.loc[ob_index]['High'], wave['label']) == False\n", "\n", "# Ob index maybe peak correction wave or any ob in range\n", "def range_tested_price_level(df, trend_status, waves, ob_index, label_key_level):\n", "    result = []\n", "    if len(trend_status) < 2:\n", "        return result\n", "    local_waves = copy.deepcopy(waves)\n", "    if waves[-1]['confirmed'] is False:\n", "        local_waves.pop()\n", "    number_waves = count_waves_from_peak_index(trend_status[1]['peak_index'], local_waves, trend_status[1]['label'])\n", "    while number_waves >= 2:\n", "        if is_wave_testing_ob(df, local_waves[1-number_waves], ob_index, label_key_level):\n", "            result.append(local_waves[1-number_waves])\n", "        number_waves = number_waves - 2\n", "    return result\n", "\n", "def find_peak_ob_correction(df, waves, key_level):\n", "    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'])\n", "    if number_waves == 0:\n", "        return None\n", "    if key_level['label']:\n", "        return df.loc[key_level['peak_index']:key_level['checked_index']]['Low'].idxmin()\n", "    else:\n", "        return df.loc[key_level['peak_index']:key_level['checked_index']]['High'].idxmax()\n", "\n", "def find_correction_wave(df, waves, key_level):\n", "    lowest_index = find_peak_ob_correction(df, waves, key_level)\n", "    return find_peak_wave_candle_belongs_to(lowest_index, waves, 1 - key_level['label'])\n", "\n", "def location_compare_level(key_level):\n", "    # if is_in_secondary_level(df, trend_status, waves, key_level):\n", "    #     return 'secondary'\n", "    if key_level.get('ob_correction_index') is None:\n", "        return 'top'\n", "    percent_latest_price = key_level['ratio_trend']\n", "    if percent_latest_price > 1:\n", "        return 'below'\n", "    if percent_latest_price <= 1 and percent_latest_price > 0.8:\n", "        return 'prebot'\n", "    if percent_latest_price <= 0.8 and percent_latest_price >= 0.618:\n", "        return '68'\n", "    if percent_latest_price <= 0.3:\n", "        return 'top'\n", "    return 'mid'\n", "\n", "def is_in_secondary_level(df, trend_status, waves, key_level):\n", "    if key_level['key_level_index'] == key_level['secondary_key_index'] or len(trend_status) < 2:\n", "        return False\n", "    ob_index = find_peak_ob_correction(df, waves, trend_status[0])\n", "    if ob_index is None:\n", "        return False\n", "    return df.loc[ob_index]['Low'] >= df.loc[key_level['secondary_key_index']]['Low'] and \\\n", "        df.loc[ob_index]['Low'] <= df.loc[key_level['secondary_key_index']]['High'] or \\\n", "        df.loc[ob_index]['High'] >= df.loc[key_level['secondary_key_index']]['Low'] and \\\n", "        df.loc[ob_index]['High'] <= df.loc[key_level['secondary_key_index']]['High']\n", "\n", "def update_status_trend(name, timeframe, df, waves, key_level, is_primary_key=False, max_stack=4):\n", "    global trend_status\n", "    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)\n", "    trend_status[name][timeframe].append(copy.deepcopy(key_level))\n", "    max_stack = max_stack - 1\n", "    if number_waves == 0 or max_stack == 0:\n", "        return\n", "    opposite_level = level_from_waves(df, waves[-number_waves:], is_primary_key)\n", "    update_status_trend(name, timeframe, df, waves, opposite_level, is_primary_key, max_stack)\n", "# init_level_from_avaiable()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_trend_status_with_new_updated_waves(name, timeframe, waves, key_level):\n", "    global old_trend_status, trend_status, df\n", "    if len(trend_status[name][timeframe]) > 0 and waves[-1]['checked_index'] <= trend_status[name][timeframe][0]['checked_index']:\n", "        print('Already update trend status latest checked index')\n", "        return\n", "    old_trend_status[name][timeframe] = copy.deepcopy(trend_status[name][timeframe])\n", "    if old_trend_status[name][timeframe][0]['key_level_index'] != key_level['key_level_index'] or\\\n", "    old_trend_status[name][timeframe][0]['peak_index'] != key_level['peak_index']:\n", "        trend_status[name][timeframe] = [copy.deepcopy(key_level)]\n", "        return\n", "    trend_status[name][timeframe][0] = copy.deepcopy(key_level)\n", "    if len(old_trend_status[name][timeframe]) == 1:\n", "        if waves[-1]['label'] != trend_status[name][timeframe][-1]['label']:\n", "            trend_status[name][timeframe].append(level_from_waves(df[name][timeframe], [waves[-1]]))\n", "        return\n", "    # Check latest level first\n", "    peak_index_wave = find_peak_index_wave_candle_belongs_to(trend_status[name][timeframe][-1]['peak_index'], waves, trend_status[name][timeframe][-1]['label'])\n", "    new_level = level_from_waves(df[name][timeframe], waves[peak_index_wave:], True, copy.deepcopy(trend_status[name][timeframe][-1]))\n", "    if new_level['key_level_index'] == trend_status[name][timeframe][-1]['key_level_index'] and \\\n", "    new_level['peak_index'] == trend_status[name][timeframe][-1]['peak_index']:\n", "        trend_status[name][timeframe][-1] = new_level\n", "        if waves[-1]['label'] != trend_status[name][timeframe][-1]['label']:\n", "            trend_status[name][timeframe].append(level_from_waves(df[name][timeframe], [waves[-1]]))\n", "        return\n", "    # Check reverse order level\n", "    trend_status[name][timeframe][-1] = new_level\n", "    check_index = -2\n", "    while trend_status[name][timeframe][check_index] != trend_status[name][timeframe][0]:\n", "        peak_wave = level_to_waves(trend_status[name][timeframe][check_index])[-1]\n", "        new_level = level_from_waves(df[name][timeframe], [peak_wave] + level_to_waves(trend_status[name][timeframe][check_index+1]), True, copy.deepcopy(trend_status[name][timeframe][check_index]))\n", "        if new_level['key_level_index'] == trend_status[name][timeframe][check_index]['key_level_index'] and \\\n", "        new_level['peak_index'] == trend_status[name][timeframe][check_index]['peak_index']:\n", "            trend_status[name][timeframe][check_index] = new_level\n", "            return\n", "        trend_status[name][timeframe][check_index] = new_level\n", "        trend_status[name][timeframe].pop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_range_and_save_range(name, timeframe):\n", "    global old_trend_status, trend_status, past_ranges, df\n", "    old = old_trend_status[name][timeframe]\n", "    new = trend_status[name][timeframe]\n", "    ranges = past_ranges[name][timeframe]\n", "    local_df = df[name][timeframe]\n", "    # Check if primary level broken or continue trend:\n", "    if old[0]['key_level_index'] != new[0]['key_level_index']:\n", "        past_ranges[name][timeframe] = []\n", "        print('Ranges reset')\n", "        return\n", "    # At least double top and double bottom to form a range\n", "    if len(old) < 5:\n", "        return\n", "    # Condition broken range\n", "    if len(old) - len(new) >= 3:\n", "        broken_trend = find_broken_trend(old, new)\n", "        range = {\n", "            'start_index': broken_trend['key_level_index'],\n", "            'end_index': new[0]['checked_index'],\n", "            'label': new[-1]['label'],\n", "        }\n", "        if range['label']:\n", "            range['valley_index'] = local_df.loc[range['start_index']:range['end_index'], 'Low'].idxmin()\n", "        else:\n", "            range['valley_index'] = local_df.loc[range['start_index']:range['end_index'], 'High'].idxmax()\n", "        print('New range added')\n", "        ranges.append(range)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_level_and_trend_status_with_new_row(name, timeframe, row):\n", "    if row is None:\n", "        return\n", "    print(f\"--Update level and trend status for {name} and timeframe: {timeframe}m\")\n", "    global df, waves, recent_level, primary_level, trend_status, old_trend_status, old_label, old_stack_level, sub_primary_level, history\n", "    previous_checked_index = waves[name][timeframe][-1]['checked_index']\n", "    for _, local_row in df[name][timeframe].loc[previous_checked_index:row.name].iloc[1:].iterrows():\n", "        print('--update waves--')\n", "        update_waves_with_new_row_df(df[name][timeframe], waves[name][timeframe], local_row)\n", "        print('--update primary level--')\n", "        old_label[name][timeframe] = copy.deepcopy(primary_level[name][timeframe]['label'])\n", "        old_stack_level[name][timeframe] = copy.deepcopy(primary_level[name][timeframe]['stack_level'])\n", "        start_index = find_peak_index_wave_candle_belongs_to(primary_level[name][timeframe]['peak_index'], waves[name][timeframe], primary_level[name][timeframe]['label'])\n", "        updated_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][start_index:], True, primary_level[name][timeframe])\n", "\n", "        wave_index_start_from_key_primary = find_peak_index_wave_candle_belongs_to(updated_primary_level['key_level_index'], waves[name][timeframe], 1 - primary_level[name][timeframe]['label']) or -1\n", "        wave_index_start_from_key_primary = wave_index_start_from_key_primary + 1\n", "\n", "        if updated_primary_level['label'] != primary_level[name][timeframe]['label'] or \\\n", "        updated_primary_level['stack_level'] != primary_level[name][timeframe]['stack_level']:\n", "            history['primary_level'].append(primary_level[name][timeframe])\n", "            # Empty all sub recent level\n", "            history['recent_level'] = []\n", "            history['sub_primary_level'] = []\n", "\n", "            updated_recent_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], False)\n", "            updated_sub_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], True)\n", "        else:\n", "            updated_recent_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], False, recent_level[name][timeframe])\n", "            updated_sub_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], True, sub_primary_level[name][timeframe])\n", "\n", "\n", "        if len(history['recent_level']) == 0 or updated_recent_level['label'] != recent_level[name][timeframe]['label'] or \\\n", "        updated_recent_level['stack_level'] != recent_level[name][timeframe]['stack_level']:\n", "            history['recent_level'].append(updated_recent_level)\n", "\n", "        if len(history['sub_primary_level']) == 0 or updated_sub_primary_level['label'] != sub_primary_level[name][timeframe]['label'] or \\\n", "        updated_sub_primary_level['stack_level'] != sub_primary_level[name][timeframe]['stack_level']:\n", "            history['sub_primary_level'].append(updated_sub_primary_level)\n", "\n", "        primary_level[name][timeframe] = updated_primary_level\n", "        recent_level[name][timeframe] = updated_recent_level\n", "        sub_primary_level[name][timeframe] = updated_sub_primary_level\n", "\n", "\n", "        # if skip_trend_status == False:\n", "        #     print('--update trend status--')\n", "        #     update_trend_status(df[name][timeframe], waves[name][timeframe], primary_level[name][timeframe], name, timeframe)\n", "        #     print('--end update trend status--')\n", "\n", "            # if len(trend_status[name][timeframe]) == 1:\n", "            #     recent_level[name][timeframe] = copy.deepcopy(primary_level[name][timeframe])\n", "            # else:\n", "            #     if (len(old_trend_status[name][timeframe]) == 1 or\\\n", "            #     old_trend_status[name][timeframe][1]['key_level_index'] != trend_status[name][timeframe][1]['key_level_index'] or\\\n", "            #     old_trend_status[name][timeframe][1]['peak_index'] != trend_status[name][timeframe][1]['peak_index']):\n", "            #         print('--update recent level correction--')\n", "            #         recent_level[name][timeframe] = update_recent_level_from_primary(df[name][timeframe], trend_status[name][timeframe][1], waves[name][timeframe])\n", "        # print('--Detect and save ranges---')\n", "        # detect_range_and_save_range(name, timeframe)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_tail_candle(candle):\n", "    if candle['label'] == 1:\n", "        return [candle['Open'], candle['Low']]\n", "    else:\n", "        return [candle['Close'], candle['Low']]\n", "\n", "def get_hair_candle(candle):\n", "    if candle['label'] == 1:\n", "        return [candle['High'], candle['Close']]\n", "    else:\n", "        return [candle['High'], candle['Open']]\n", "\n", "def get_body_candle(candle):\n", "    if candle['label'] == 1:\n", "        return [candle['Close'], candle['Open']]\n", "    else:\n", "        return [candle['Open'], candle['Close']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_nosd_candles_pair(first_candle, second_candle, label):\n", "    if first_candle['label'] == label or second_candle['label'] != label:\n", "        return False\n", "    if label == 1:\n", "        return second_candle['Low'] < first_candle['Low']\n", "    else:\n", "        return second_candle['High'] > first_candle['High']\n", "\n", "def is_outsidebar_candles_pair(first_candle, second_candle, label):\n", "    if is_nosd_candles_pair(first_candle, second_candle, label) == False:\n", "        return False\n", "    if label == 1:\n", "        return second_candle['Close'] >= first_candle['High']\n", "    else:\n", "        return second_candle['Close'] <= first_candle['Low']\n", "\n", "def is_eg_pair(first_candle, second_candle, label):\n", "    if first_candle['label'] == label or second_candle['label'] != label:\n", "        return False\n", "    if label == 1:\n", "        return is_eg_candle(second_candle) and second_candle['Close'] >= first_candle['High']\n", "    else:\n", "        return is_eg_candle(second_candle) and second_candle['Close'] <= first_candle['Low']\n", "\n", "def is_meramera_candles_pair(first_candle, second_candle, label):\n", "    if is_outsidebar_candles_pair(first_candle, second_candle, label) == False:\n", "        return False\n", "    body_first_candle = get_body_candle(first_candle)\n", "    if label == 1:\n", "        tail_second_candle = get_tail_candle(second_candle)\n", "        return body_first_candle[0] <= tail_second_candle[0] and body_first_candle[1] >= tail_second_candle[1]\n", "    else:\n", "        hair_second_candle = get_hair_candle(second_candle)\n", "        return body_first_candle[0] <= hair_second_candle[0] and body_first_candle[1] >= hair_second_candle[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pattern_reg(df, wave, label, type_candle_pair_func):\n", "    start_index = df.index.get_loc(wave['start_index'])\n", "    checked_index = df.index.get_loc(wave['checked_index'])\n", "    index_list = []\n", "    for index in range(start_index, checked_index):\n", "        first_candle = df.iloc[index-1]\n", "        second_candle = df.iloc[index]\n", "        if first_candle['label'] == label or second_candle['label'] != label:\n", "            continue\n", "        if type_candle_pair_func(first_candle, second_candle, label):\n", "            index_list.append(first_candle.name)\n", "    return index_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_two_candle_spliting(df, first_index, second_index):\n", "    return df.loc[first_index]['Low'] > df.loc[second_index]['High'] or df.loc[second_index]['Low'] > df.loc[first_index]['High']\n", "\n", "def is_two_candle_touching(df, first_index, second_index):\n", "    return is_two_candle_spliting(df, first_index, second_index) == False\n", "\n", "def find_closest_ob_above_peak_wave(df, waves, key_level):\n", "    wave_label = key_level['label']\n", "    # Find break candle that is not lie on current wave\n", "    if wave_label:\n", "        price_top = df.loc[key_level['peak_index']]['Close'] + df.loc[key_level['start_index']]['ma_candle'] * ratio_ma_sl\n", "    else:\n", "        price_top = df.loc[key_level['peak_index']]['Close'] - df.loc[key_level['start_index']]['ma_candle'] * ratio_ma_sl\n", "    break_candle_index = find_break_candle(df, key_level['start_index'], price_top, 1 - wave_label)\n", "    if break_candle_index:\n", "        wave_break_candle = find_peak_wave_candle_belongs_to(break_candle_index, waves, 1 - wave_label)\n", "        previous_candle = break_candle_index\n", "        while df.loc[previous_candle]['label'] == 1 - wave_label or is_two_candle_touching(df, key_level['peak_index'], previous_candle):\n", "            if wave_break_candle is None or df.loc[previous_candle].name <= wave_break_candle['start_index']:\n", "                return None\n", "            previous_candle = df.iloc[df.index.get_loc(previous_candle) - 1].name\n", "        return previous_candle\n", "    return None\n", "\n", "def find_wave_index_candle_belongs_to(candle_index, waves):\n", "    check_index = -1\n", "    while True:\n", "        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['checked_index'] >= candle_index:\n", "            return check_index\n", "        check_index = check_index - 1\n", "        if check_index*-1 > len(waves):\n", "            return None\n", "        if waves[check_index]['start_index'] < candle_index and waves[check_index]['checked_index'] < candle_index:\n", "            return check_index + 1\n", "\n", "def find_peak_wave_candle_belongs_to(candle_index, waves, label_wave):\n", "    check_index = -1\n", "    while True:\n", "        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] >= candle_index and waves[check_index]['label'] == label_wave:\n", "            return waves[check_index]\n", "        check_index = check_index - 1\n", "        if check_index*-1 > len(waves):\n", "            return None\n", "        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] <= candle_index and waves[check_index]['label'] == 1 - label_wave:\n", "            return waves[check_index + 1]\n", "\n", "def find_peak_index_wave_candle_belongs_to(candle_index, waves, label_wave):\n", "    check_index = -1\n", "    while True:\n", "        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] >= candle_index and waves[check_index]['label'] == label_wave:\n", "            return check_index\n", "        check_index = check_index - 1\n", "        if check_index*-1 > len(waves):\n", "            return None\n", "        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] <= candle_index and waves[check_index]['label'] == 1 - label_wave:\n", "            return check_index + 1\n", "\n", "def lowest_index_from_to(df, waves, start_index, end_index, start_label, end_label):\n", "    start_wave_index = find_peak_index_wave_candle_belongs_to(start_index, waves, start_label)\n", "    end_wave_index = find_peak_index_wave_candle_belongs_to(end_index, waves, end_label)\n", "    count = end_wave_index - start_wave_index\n", "    print(f\"Count waves from old key: {count}\")\n", "    if count == 0:\n", "        return start_index\n", "    if count == 1:\n", "        return waves[start_wave_index+1]['peak_index']\n", "    if start_label:\n", "        return df.loc[waves[start_wave_index+1]['start_index']:waves[end_wave_index]['start_index'], 'Low'].idxmin()\n", "    else:\n", "        return df.loc[waves[start_wave_index+1]['start_index']:waves[end_wave_index]['start_index'], 'High'].idxmax()\n", "\n", "def find_closest_ob_below_key_level(df, waves, key_level):\n", "    if key_level['secondary_key_index'] == key_level['key_level_index']:\n", "        return None\n", "    last_wave = find_peak_wave_candle_belongs_to(key_level['key_level_index'], waves, 1 - key_level['label'])\n", "    if last_wave:\n", "        return find_closest_ob_above_peak_wave(df, waves, last_wave)\n", "    return None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_unconfirm_range_break(old_trend_status, trend_status):\n", "    return old_trend_status and old_trend_status[-1]['confirmed'] == False and trend_status[-1]['confirmed'] == True and trend_status[-1]['label'] == trend_status[0]['label']\n", "\n", "def cal_ratio_trend(df, check_index, key_level):\n", "    if key_level['peak_index'] == key_level['key_level_index']:\n", "        return -1\n", "    if key_level['label']:\n", "        top = 'High'\n", "        bot = 'Low'\n", "    else:\n", "        top = 'Low'\n", "        bot = 'High'\n", "    return (df.loc[key_level['peak_index']][top] - df.loc[check_index][bot])/(df.loc[key_level['peak_index']][top] - df.loc[key_level['key_level_index']][bot])\n", "\n", "def ratio_trend_to_price(df, ratio, key_level):\n", "    if df.loc[key_level['peak_index']]['Close'] == df.loc[key_level['key_level_index']]['Close']:\n", "        raise ValueError('Invalid input to cal price')\n", "    if key_level['label']:\n", "        direction = 1\n", "    else:\n", "        direction = -1\n", "    return df.loc[key_level['key_level_index'], 'Close'] + direction*abs(df.loc[key_level['peak_index']]['Close'] - df.loc[key_level['key_level_index']]['Close'])*(1-ratio)\n", "\n", "def last_trend_with_different_label(trends):\n", "    for trend in reversed(trends):\n", "        if trend['label'] != trends[0]['label']:\n", "            return trend\n", "    return None\n", "\n", "def is_trend_get_liquidity(df, trend, waves):\n", "    if trend['old_key_level_index'] == None or trend['old_peak_index'] == None:\n", "        return False\n", "    if trend['label']:\n", "        low = 'Low'\n", "        direction = 1\n", "    else:\n", "        low = 'High'\n", "        direction = -1\n", "    low_index = lowest_index_from_to(df, waves, trend['old_key_level_index'], trend['old_peak_index'], trend['label'], trend['label'])\n", "    return (df.loc[trend['key_level_index'], low] - df.loc[low_index, low])*direction < df.loc[low_index, 'ma_candle'] * liquidity_ratio\n", "\n", "def correction_broken(df, high_df, waves, old_trend_status, trend_status, high_trend_status, recent_level):\n", "    if trend_status[0]['label'] != old_trend_status[0]['label']:\n", "        return False\n", "    if cal_ratio_trend(df, waves[-1]['checked_index'], trend_status[0]) >= 0.3:\n", "        if len(trend_status) == 1 or len(old_trend_status) == 1:\n", "            return False\n", "        if trend_status[1]['label'] != trend_status[0]['label'] or trend_status[1]['label'] == old_trend_status[1]['label']:\n", "            return False\n", "    else:\n", "        if len(trend_status) != 1 or trend_status[0]['stack_level'] <= old_trend_status[0]['stack_level']:\n", "            return False\n", "    if len(high_trend_status) > 1 and high_trend_status[1]['type_wave'] == 'trend':\n", "        if trend_status[0]['label'] != high_trend_status[1]['label']:\n", "            return False\n", "    else:\n", "        if trend_status[0]['label'] != high_trend_status[0]['label']:\n", "            return False\n", "    # Filter liquidity\n", "    return is_trend_get_liquidity(df, trend_status[0], waves)\n", "\n", "def is_candle_belong_peak_level(key_level, candle_index):\n", "    pass\n", "\n", "def zanshin1(df, high_df, high_waves, old_trend_status, trend_status, high_trend_status, recent_level):\n", "    global waiting_stack_level_confirm, ratio_before, ratio_after\n", "    if waiting_stack_level_confirm and old_trend_status[0]['label'] == trend_status[0]['label'] and trend_status[0]['stack_level'] > old_trend_status[0]['stack_level'] and trend_status[0]['stack_level'] >= 1:\n", "        waiting_stack_level_confirm = False\n", "        return True, None\n", "    if old_trend_status[0]['label'] == trend_status[0]['label']:\n", "        return False, None\n", "    if trend_status[0]['label'] != high_trend_status[0]['label']:\n", "        waiting_stack_level_confirm = False\n", "        return False, None\n", "    if count_waves_from_peak_index(high_trend_status[0]['peak_index'], high_waves, high_trend_status[0]['label']) > 0 and cal_ratio_trend(high_df, high_trend_status[0]['checked_index'], high_trend_status[0]) < 0.3:\n", "        return False, None\n", "    waiting_stack_level_confirm = True\n", "    return False, None\n", "\n", "def is_wave_have_range(check_index, waves, label):\n", "    wave = find_peak_wave_candle_belongs_to(check_index, waves, label)\n", "    return len(wave['ranges']) > 1 or wave['ranges'][0]['count'] > 11\n", "\n", "def is_double_range_break(break_candle, waves, label):\n", "    wave_index = find_wave_index_candle_belongs_to(break_candle, waves)\n", "    count = 0\n", "    while wave_index <= -1:\n", "        if waves[wave_index]['label'] == label:\n", "            for range in waves[wave_index]['ranges']:\n", "                if range['count'] >= 8:\n", "                    count = count + 1\n", "                if range['count'] >= 30:\n", "                    return True\n", "        else:\n", "            count = 0\n", "        if count == 2:\n", "            return True\n", "        wave_index = wave_index + 1\n", "    return False\n", "\n", "def is_price_on_edge(df_key, key, df_check_index, check_index, label_wave):\n", "    if label_wave == key['label']:\n", "        price = df_check_index.loc[check_index]['High']\n", "        return price >= df_key.loc[key['peak_index'], 'Low']\n", "    else:\n", "        price = df_check_index.loc[check_index]['Low']\n", "        return price <= df_key.loc[key['key_level_index'], 'High']\n", "\n", "\n", "def zanshin2(df, high_df, waves, old_trend_status, trend_status, high_trend_status, high_key):\n", "    global broken_trendline, is_trendline_broken\n", "    if is_trendline_broken:\n", "        if len(trend_status) <= 1 or len(old_trend_status) <= 1:\n", "            is_trendline_broken = False\n", "            return False, None\n", "        if trend_status[1]['label'] == old_trend_status[1]['label'] and trend_status[1]['stack_level'] > old_trend_status[1]['stack_level']:\n", "                is_trendline_broken = False\n", "                return False, None\n", "        else:\n", "            if is_price_on_edge(high_df, high_key, df, trend_status[0]['checked_index'], trend_status[0]['label']):\n", "                is_trendline_broken = False\n", "                return False, None\n", "            if is_double_range_break(broken_trendline['break_candle'], waves, trend_status[0]['label']) or \\\n", "                trend_status[-1]['label'] == trend_status[0]['label'] and trend_status[-1]['stack_level'] >= 2:\n", "                is_trendline_broken = False\n", "                return True, None\n", "            return False, None\n", "    if len(trend_status) > 1 and trend_status[1]['label'] != trend_status[0]['label'] and is_break_trend_line(df, waves, trend_status[1]):\n", "        is_trendline_broken = True\n", "        broken_trendline = {\n", "            'start_index': trend_status[1]['old_key_level_index'],\n", "            'end_index': trend_status[1]['key_level_index'],\n", "            'label': trend_status[1]['label'],\n", "            'break_candle': trend_status[1]['checked_index']\n", "        }\n", "    return False, None\n", "\n", "\n", "def is_higher_peak_correction(df, trend_status, label_correction):\n", "    last_trend = trend_status[-1]\n", "    if last_trend['label'] != label_correction:\n", "        return False\n", "    if label_correction == 0:\n", "        peak = 'Low'\n", "        direction = 1\n", "    else:\n", "        peak = 'High'\n", "        direction = -1\n", "    peak_price = df.loc[last_trend['peak_index'], peak]\n", "    for trend in reversed(trend_status):\n", "        if trend['label'] != label_correction:\n", "            check_index = trend['start_index']\n", "        else:\n", "            check_index = trend['peak_index']\n", "        if (df.loc[check_index][peak] - peak_price)*direction < 0:\n", "            return False\n", "    return True\n", "\n", "def range_fake_breakout(df, waves, old_trend_status, trend_status, high_trend_status, low_trend_status):\n", "    global break_index\n", "    if is_wave_in_range(high_trend_status):\n", "        return False\n", "    if trend_status[0]['label'] != old_trend_status[0]['label']:\n", "        return False\n", "    if len(trend_status) == 1 or len(old_trend_status) < 4:\n", "        return False\n", "    # case 1: real break range but not break key level\n", "    if trend_status[1]['label'] != trend_status[0]['label'] and len(trend_status) == 2:\n", "        break_index = trend_status[1]['secondary_key_index']\n", "        print('fake_breakout_type1')\n", "        return True\n", "    # case 2: fake range breakout\n", "    if is_higher_peak_correction(df, trend_status, 1 - trend_status[0]['label']):\n", "        print('fake_breakout_type2')\n", "        return True\n", "    return False\n", "\n", "\n", "def range_fake_breakout_confirm(df, waves, old_trend_status, trend_status):\n", "    global fake_breakout_checking, break_index\n", "    if trend_status[0]['label'] != old_trend_status[0]['label'] or len(trend_status) == 1:\n", "        # primary trend change\n", "        fake_breakout_checking = False\n", "        break_index = None\n", "        return False\n", "    if len(trend_status) == 2 and trend_status[1]['label'] == trend_status[0]['label']:\n", "        fake_breakout_checking = False\n", "        break_index = None\n", "        return False\n", "    if trend_status[0]['label'] == 1:\n", "        high = 'High'\n", "        direction = 1\n", "    else:\n", "        high = 'Low'\n", "        direction = -1\n", "    if (df.loc[waves[-1]['peak_index']]['Close'] - df.loc[break_index][high])*direction >= 0:\n", "        return True\n", "    return False\n", "\n", "def main_trend_continue(df, waves, old_trend_status, trend_status, high_trend_status):\n", "    if trend_status[0]['label'] != old_trend_status[0]['label']:\n", "        return False\n", "    if trend_status[0]['stack_level'] > 0 and is_trend_has_imbalance(df, trend_status[0]):\n", "        return True\n", "    return False\n", "\n", "def is_wave_range_break(old_trend_status, trend_status):\n", "    if is_wave_in_range(old_trend_status) == False:\n", "        return False\n", "    return len(trend_status) <= 2 and trend_status[-1]['label'] == trend_status[0]['label']\n", "\n", "def is_wave_in_range(trend_status):\n", "    return len(trend_status) >= 3\n", "\n", "def is_weak_trend(df, trend_status):\n", "    if trend_status['secondary_key_index'] != trend_status['key_level_index']:\n", "        ratio = cal_ratio_trend(df, trend_status['secondary_key_index'], trend_status)\n", "        return ratio < weak_trend_ratio\n", "    else:\n", "        return False\n", "\n", "def is_break_trend_line(df, waves, recent_level):\n", "    # Previous low not working need fix\n", "    previous_low = recent_level['old_key_level_index']\n", "    if previous_low == None:\n", "        return False\n", "    # Find func trend line:\n", "    if recent_level['label'] == 1:\n", "        direction = 1\n", "    else:\n", "        direction = -1\n", "\n", "    y2 = df.loc[recent_level['key_level_index'], 'Close']\n", "    y1 = df.loc[previous_low, 'Close']\n", "    x2 = df.index.get_loc(recent_level['key_level_index'])\n", "    x1 = df.index.get_loc(previous_low)\n", "    m1 = (y2 - y1) / (x2 - x1)\n", "    c1 = y1 - m1 * x1\n", "    x_intersect = df.index.get_loc(waves[-1]['checked_index'])\n", "    y_intersect = m1 * x_intersect + c1\n", "\n", "    return (df.loc[waves[-1]['checked_index'], 'Close'] - y_intersect)*direction < 0\n", "\n", "\n", "def is_wave_has_valid_range(waves):\n", "    count_each = 0\n", "    for wave in waves:\n", "        if len(wave['ranges']) >=2:\n", "            count = 0\n", "            for range in wave['ranges']:\n", "                count = count + range['count']\n", "            if count >= 20:\n", "                return True\n", "        else:\n", "            if wave['confirmed']:\n", "                count_each = count_each + wave['ranges'][0]['count']\n", "            else:\n", "                count_each = count_each + len(wave)\n", "            if count_each >= 20:\n", "                return True\n", "    return False\n", "\n", "\n", "def find_previous_lowest_index_same_label(df, waves, level, is_primary=False):\n", "    number_waves = count_waves_from_peak_index(level['peak_index'], waves, level['label']) + 1\n", "    if len(waves) == number_waves:\n", "        return None\n", "    previous_level = level_from_waves(df, waves[:-number_waves], is_primary)\n", "    if previous_level['label'] != level['label']:\n", "        # Using peak previous level different label as lowest point\n", "        return previous_level['peak_index']\n", "    return previous_level['key_level_index']\n", "\n", "def is_testing_key_level(df, key_level, check_index):\n", "    if key_level['label']:\n", "        return df.loc[key_level['key_level_index']]['High'] >= df.loc[check_index]['Low']\n", "    else:\n", "        return df.loc[key_level['key_level_index']]['Low'] <= df.loc[check_index]['High']\n", "\n", "def break_range_reverse(df, waves, key_level, trend_status, old_trend_status):\n", "    if old_trend_status[0]['label'] != trend_status[0]['label']:\n", "        return False, None\n", "    if len(trend_status) == 1 or is_testing_key_level(df, key_level, waves[-1]['checked_index']) == False:\n", "        return False, None\n", "    if len(trend_status) == 2 and trend_status[1]['stack_level'] >= 1:\n", "        if is_wave_has_valid_range(waves[-2:]):\n", "            return True, None\n", "    if len(trend_status) >= 3:\n", "        return True, None\n", "    return False, None\n", "\n", "def bos_detect(df, waves, key_level, trend_status, old_trend_status):\n", "    global bos_signal, lowest_point, bos_checked_index, bos_index\n", "    if key_level['label']:\n", "        direction = 1\n", "        low = 'Low'\n", "        high = 'High'\n", "    else:\n", "        direction = -1\n", "        low = 'High'\n", "        high = 'Low'\n", "    if bos_signal:\n", "        if (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[lowest_point][low])*direction < 0 or\\\n", "        key_level['label'] != old_trend_status[0]['label'] or\\\n", "        key_level['stack_level'] != old_trend_status[0]['stack_level']:\n", "            bos_signal = False\n", "            return False, None\n", "        if (df.loc[waves[-1]['checked_index']]['Close'] - df.loc[bos_index]['Close'])*direction > 0:\n", "            if key_level['label']:\n", "                bos_checked_index = df.loc[bos_index:waves[-1]['checked_index']][low].idxmin()\n", "            else:\n", "                bos_checked_index = df.loc[bos_index:waves[-1]['checked_index']][low].idxmax()\n", "            if (df.loc[bos_index][high]-df.loc[bos_checked_index][low])/(df.loc[bos_index][high]-df.loc[lowest_point][low]) < 0.5:\n", "                bos_index = waves[-1]['checked_index']\n", "                return False, None\n", "            else:\n", "                # Check effort\n", "                if df.loc[lowest_point:bos_index]['Volume'].sum()/abs(df.loc[lowest_point]['Open'] - df.loc[bos_index]['Close']) >\\\n", "                df.loc[bos_checked_index:waves[-1]['checked_index']]['Volume'].sum()/abs(df.loc[bos_checked_index]['Open'] - df.loc[waves[-1]['checked_index']]['Close']):\n", "                    bos_signal = False\n", "                    return True, None\n", "                else:\n", "                    bos_index = waves[-1]['checked_index']\n", "                    return False, None\n", "\n", "        # checked_candles = df.loc[bos_checked_index:waves[-1]['checked_index']]\n", "        # if len(checked_candles) >= 2 and (checked_candles.iloc[1]['Close'] - checked_candles.iloc[0]['Close'])*direction > 0 and\\\n", "        # (checked_candles.iloc[1]['Close'] - checked_candles.iloc[0]['Open'])*direction > 0 and \\\n", "        # (checked_candles.iloc[1]['effort'] - checked_candles.iloc[0]['effort'])*direction < 0 and is_eg_candle(checked_candles.iloc[1]):\n", "        #     bos_signal = False\n", "        #     return True, None\n", "        # else:\n", "        #     bos_checked_index = df.loc[bos_checked_index:].iloc[1].name if len(checked_candles) >= 2 else bos_checked_index\n", "        #     return False, None\n", "    if bos_signal == False and len(trend_status) > 1 and trend_status[1]['label'] != key_level['label'] and (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[trend_status[1]['key_level_index']]['Close'])*direction > 0 and\\\n", "    cal_ratio_trend(df, trend_status[1]['peak_index'], trend_status[0]) >= 0.5:\n", "        bos_signal = True\n", "        lowest_point = copy.deepcopy(trend_status[1]['peak_index'])\n", "        bos_index = copy.deepcopy(waves[-1]['checked_index'])\n", "        bos_checked_index = bos_index\n", "    return False, None\n", "\n", "def smc(name, timeframe, high_tf, very_high_tf):\n", "    global df, primary_level, old_trend_status, trend_status\n", "    if old_trend_status[name][timeframe][0]['label'] != trend_status[name][timeframe][0]['label'] or old_trend_status[name][timeframe][0]['stack_level'] != trend_status[name][timeframe][0]['stack_level']:\n", "        # Key level change peak or key, find smc ob\n", "        primary_level[name][timeframe]['smc_ob_list'] = find_smc_ob(df[name][timeframe], primary_level[name][timeframe])\n", "    if primary_level[name][timeframe].get('smc_ob_list') is None:\n", "        return False, None\n", "    if primary_level[name][high_tf]['label'] != primary_level[name][timeframe]['label'] or primary_level[name][timeframe]['label'] != primary_level[name][very_high_tf]['label']:\n", "        return False, None\n", "    if primary_level[name][timeframe]['label']:\n", "        high = 'High'\n", "        low = 'Low'\n", "        direction = 1\n", "    else:\n", "        high = 'Low'\n", "        low = 'High'\n", "        direction = -1\n", "    for ob in primary_level[name][timeframe].get('smc_ob_list'):\n", "       if (df[name][timeframe].loc[ob['ob_index']][high] - df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']][low])*direction > 0:\n", "           return True, None\n", "    return False, None\n", "\n", "def confluent_key(name, timeframe, high_tf, very_high_tf):\n", "    global df, primary_level, old_trend_status, trend_status\n", "    if primary_level[name][high_tf]['label'] != primary_level[name][very_high_tf]['label'] or primary_level[name][timeframe]['label'] != primary_level[name][high_tf]['label']:\n", "        return False, None\n", "    #  Is test discount zone\n", "    if primary_level[name][high_tf].get('ratio_trend') and (primary_level[name][high_tf]['ratio_trend'] < 0.618 or primary_level[name][very_high_tf]['ratio_trend'] < 0.618):\n", "        return False, None\n", "    if primary_level[name][timeframe]['label']:\n", "        high = 'High'\n", "        low = 'Low'\n", "        direction = 1\n", "    else:\n", "        high = 'Low'\n", "        low = 'High'\n", "        direction = -1\n", "    if (df[name][timeframe].loc[primary_level[name][timeframe]['key_level_index']][high] - df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']][low])*direction > 0:\n", "        entry = df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']]['Close']\n", "        if primary_level[name][high_tf]['label'] == primary_level[name][timeframe]['label']:\n", "            ob_sl, tf_sl = primary_level[name][timeframe]['key_level_index'], timeframe\n", "        else:\n", "            ob_sl, tf_sl = primary_level[name][timeframe]['peak_index'], timeframe\n", "        ob_tp = primary_level[name][very_high_tf]['peak_index']\n", "        return True, (entry, ob_sl, tf_sl , ob_tp, very_high_tf, primary_level[name][high_tf]['label'],)\n", "    return False, None\n", "\n", "def trend_reverse(name, timeframe, high_tf, very_high_tf):\n", "    global old_label, old_stack_level, primary_level\n", "    if old_label[name][timeframe] != primary_level[name][timeframe]['label']:\n", "        return True, None\n", "    return False, None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_coc(name, main_tf, high_tf, very_high_tf):\n", "    global old_trend_status, trend_status, df, waves, fake_breakout_checking, primary_level, recent_level\n", "\n", "    # if correction_broken(df[name][main_tf], df[name][high_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], recent_level[name][timeframe]):\n", "    #     return 'correction_broken'\n", "\n", "    # if location in coc_pattern_point['main_trend_continue']['location'] and main_trend_continue(df[name][main_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf]):\n", "    #     return 'main_trend_continue'\n", "\n", "    # result_zanshin1, sl_tp = zanshin1(df[name][main_tf], df[name][high_tf], waves[name][high_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], recent_level[name][timeframe])\n", "    # if result_zanshin1:\n", "    #     return 'zanshin1', sl_tp\n", "\n", "    # result_zanshin2, entry_sl_tp = zanshin2(df[name][main_tf], df[name][high_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], primary_level[name][high_tf])\n", "    # if result_zanshin2:\n", "    #     return 'zanshin2', entry_sl_tp\n", "\n", "    # result_bos, entry_sl_tp = bos_detect(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], trend_status[name][main_tf], old_trend_status[name][timeframe])\n", "    # if result_bos:\n", "    #     return 'bos', entry_sl_tp\n", "\n", "    # result_brr, entry_sl_tp = break_range_reverse(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], trend_status[name][main_tf], old_trend_status[name][timeframe])\n", "    # if result_brr:\n", "    #     return 'break_range_reverse', entry_sl_tp\n", "\n", "    # result_confluent_key, entry_sl_tp = confluent_key(name, main_tf, high_tf, very_high_tf)\n", "    # if result_confluent_key:\n", "    #     return 'confluent_key', entry_sl_tp\n", "\n", "    result_trend_reverse, entry_sl_tp = trend_reverse(name, main_tf, high_tf, very_high_tf)\n", "    if result_trend_reverse:\n", "        return 'trend_reverse', entry_sl_tp\n", "\n", "    # result_smc, entry_sl_tp = smc(name, main_tf, high_tf, very_high_tf)\n", "    # if result_smc:\n", "    #     return 'smc', entry_sl_tp\n", "\n", "\n", "    # result, entry_sl_tp = range_fake_breakout(df[name][main_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf]):\n", "    # if result:\n", "    #     return 'range_fake_breakout', entry_sl_tp\n", "\n", "\n", "    # if is_wave_range_break(old_trend_status[name][main_tf], trend_status[name][main_tf]):\n", "    #     return 'is_wave_range_break'\n", "    # if is_low_tf_opposite_recent_reverse(old_trend_status[name][low_tf], trend_status[name][low_tf]):\n", "    #     return 'is_low_tf_opposite_recent_reverse'\n", "    # if is_unconfirm_range_break(old_trend_status[name][main_tf], trend_status[name][main_tf]):\n", "    #     return 'is_unconfirm_range_break'\n", "    # opposite_recent_level = opposite_level_from_key_level(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], False)\n", "    # if opposite_recent_level != None and is_break_trend_line(df[name][main_tf], waves[name][main_tf], opposite_recent_level):\n", "    #     return 'is_break_trend_line'\n", "    return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def candle_pattern(df, wave, label):\n", "    for name, candle in candles_pattern_point.items():\n", "        if len(pattern_reg(df, wave, label, candle['reg'])) >= candle['count']:\n", "            return name\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def total_point_from_signal_code(signal_code):\n", "    total = location_point[signal_code[0]] + coc_pattern_point[signal_code[1]]['point']\n", "    if signal_code[2]:\n", "        total = total + candles_pattern_point[signal_code[2]]['point']\n", "    return total"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_trend_status(df, waves, key_level, name, timeframe):\n", "    global old_trend_status, trend_status\n", "    if len(trend_status[name][timeframe]) > 0 and waves[-1]['checked_index'] <= trend_status[name][timeframe][0]['checked_index']:\n", "        print('Already update trend status latest checked index')\n", "        return\n", "    old_trend_status[name][timeframe] = copy.deepcopy(trend_status[name][timeframe])\n", "    trend_status[name][timeframe] = []\n", "    update_status_trend(name, timeframe, df, waves, key_level, False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def opposite_ob(wave, df):\n", "    start_date = wave['start_index']\n", "    label = wave['label']\n", "    result = []\n", "\n", "    # Get all dates after start_date (exclusive)\n", "    dates = df.index[df.index > start_date]\n", "\n", "    for date in dates:\n", "        pos = df.index.get_loc(date)\n", "        # Check candle type based on wave label\n", "        if label == 1:\n", "            # Up wave: look for red candles (close < open)\n", "            if df.loc[date, 'Close'] >= df.loc[date, 'Open']:\n", "                continue\n", "            # Get adjacent candles\n", "            prev_pos = pos - 1\n", "            next_pos = pos + 1\n", "            prev_date = df.index[prev_pos] if prev_pos >= 0 else None\n", "            next_date = df.index[next_pos] if next_pos < len(df.index) else None\n", "            # Collect low prices\n", "            lows = []\n", "            if prev_date is not None:\n", "                lows.append(df.loc[prev_date, 'Low'])\n", "            lows.append(df.loc[date, 'Low'])\n", "            if next_date is not None:\n", "                lows.append(df.loc[next_date, 'Low'])\n", "            # Check if current date's low is the minimum\n", "            if df.loc[date, 'Low'] == min(lows):\n", "                result.append(date)\n", "        else:\n", "            # Down wave: look for green candles (close > open)\n", "            if df.loc[date, 'Close'] <= df.loc[date, 'Open']:\n", "                continue\n", "            # Get adjacent candles\n", "            prev_pos = pos - 1\n", "            next_pos = pos + 1\n", "            prev_date = df.index[prev_pos] if prev_pos >= 0 else None\n", "            next_date = df.index[next_pos] if next_pos < len(df.index) else None\n", "            # Collect high prices\n", "            highs = []\n", "            if prev_date is not None:\n", "                highs.append(df.loc[prev_date, 'High'])\n", "            highs.append(df.loc[date, 'High'])\n", "            if next_date is not None:\n", "                highs.append(df.loc[next_date, 'High'])\n", "            # Check if current date's high is the maximum\n", "            if df.loc[date, 'High'] == max(highs):\n", "                result.append(date)\n", "    return result\n", "\n", "def get_trendline(wave, df):\n", "    start_date = wave['start_index']\n", "    ob_dates = opposite_ob(wave, df)\n", "    all_dates = [start_date] + ob_dates\n", "    all_dates_sorted = sorted(all_dates)\n", "\n", "    # Collect relevant price points\n", "    label = wave.label\n", "    points = []\n", "    for date in all_dates_sorted:\n", "        if label == 1:\n", "            price = df.loc[date, 'Low']\n", "        else:\n", "            price = df.loc[date, 'High']\n", "        x = date.toordinal()\n", "        points.append((x, price))\n", "\n", "    if len(points) < 1:\n", "        return (0.0, 0.0)  # Fallback, though all_dates includes start_date\n", "\n", "    # If only one point, return horizontal line\n", "    if len(points) == 1:\n", "        return (0.0, points[0][1])\n", "\n", "    best_slope = -np.inf if label == 1 else np.inf\n", "    best_intercept = None\n", "\n", "    # Check all pairs of points\n", "    n = len(points)\n", "    for i in range(n):\n", "        for j in range(i + 1, n):\n", "            x1, y1 = points[i]\n", "            x2, y2 = points[j]\n", "            if x2 == x1:\n", "                continue  # Avoid division by zero\n", "            slope = (y2 - y1) / (x2 - x1)\n", "            intercept = y1 - slope * x1\n", "\n", "            # Check if all points are on the correct side of the line\n", "            valid = True\n", "            for (xk, yk) in points:\n", "                y_line = slope * xk + intercept\n", "                if label == 1:\n", "                    if yk < y_line - 1e-9:  # Allowing for floating point precision\n", "                        valid = False\n", "                        break\n", "                else:\n", "                    if yk > y_line + 1e-9:\n", "                        valid = False\n", "                        break\n", "            if valid:\n", "                if label == 1:\n", "                    if slope > best_slope:\n", "                        best_slope = slope\n", "                        best_intercept = intercept\n", "                else:\n", "                    if slope < best_slope:\n", "                        best_slope = slope\n", "                        best_intercept = intercept\n", "\n", "    # Handle case where no valid line found (use first and last point)\n", "    if best_intercept is None:\n", "        x1, y1 = points[0]\n", "        x2, y2 = points[-1]\n", "        if x2 == x1:\n", "            best_slope = 0.0\n", "            best_intercept = y1\n", "        else:\n", "            best_slope = (y2 - y1) / (x2 - x1)\n", "            best_intercept = y1 - best_slope * x1\n", "\n", "    return (best_slope, best_intercept)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_maru<PERSON><PERSON>(row, direction):\n", "    body = abs(row['Close'] - row['Open'])\n", "    total_range = row['High'] - row['Low']\n", "    if total_range == 0:\n", "        return False\n", "    body_ratio = body / total_range\n", "    if direction == 'green' and row['Close'] > row['Open']:\n", "        return body_ratio >= 0.9\n", "    elif direction == 'red' and row['Close'] < row['Open']:\n", "        return body_ratio >= 0.9\n", "    return False\n", "\n", "def find_obs(df):\n", "    obs = []\n", "    for i in range(len(df) - 2):\n", "        current = df.iloc[i]\n", "        next1 = df.iloc[i+1]\n", "        next2 = df.iloc[i+2]\n", "        # Check for bullish OB (red followed by two green marubozu)\n", "        if current['Close'] < current['Open']:\n", "            if is_ma<PERSON><PERSON><PERSON>(next1, 'green') and is_ma<PERSON><PERSON><PERSON>(next2, 'green'):\n", "                obs.append({'index': i, 'type': 'bullish', 'key_level': current['Low']})\n", "        # Check for bearish OB (green followed by two red marubozu)\n", "        elif current['Close'] > current['Open']:\n", "            if is_ma<PERSON><PERSON><PERSON>(next1, 'red') and is_ma<PERSON><PERSON><PERSON>(next2, 'red'):\n", "                obs.append({'index': i, 'type': 'bearish', 'key_level': current['High']})\n", "    return obs\n", "\n", "def find_double_tops_bottoms(df, threshold_pct=0.005):\n", "    peaks = []\n", "    troughs = []\n", "    for i in range(1, len(df)-1):\n", "        prev = df.iloc[i-1]\n", "        current = df.iloc[i]\n", "        next_c = df.iloc[i+1]\n", "        if current['High'] > prev['High'] and current['High'] > next_c['High']:\n", "            peaks.append(i)\n", "        if current['Low'] < prev['Low'] and current['Low'] < next_c['Low']:\n", "            troughs.append(i)\n", "\n", "    double_tops = []\n", "    for i in range(len(peaks)):\n", "        for j in range(i+1, len(peaks)):\n", "            peak1_high = df.iloc[peaks[i]]['High']\n", "            peak2_high = df.iloc[peaks[j]]['High']\n", "            if abs(peak1_high - peak2_high) / peak1_high <= threshold_pct:\n", "                trough_between = [t for t in troughs if peaks[i] < t < peaks[j]]\n", "                if trough_between:\n", "                    trough_low = df.iloc[trough_between[0]]['Low']\n", "                    for k in range(peaks[j], len(df)):\n", "                        if df.iloc[k]['Low'] < trough_low:\n", "                            double_tops.append({'type': 'top', 'key_level': max(peak1_high, peak2_high)})\n", "                            break\n", "                    break\n", "\n", "    double_bottoms = []\n", "    for i in range(len(troughs)):\n", "        for j in range(i+1, len(troughs)):\n", "            trough1_low = df.iloc[troughs[i]]['Low']\n", "            trough2_low = df.iloc[troughs[j]]['Low']\n", "            if abs(trough1_low - trough2_low) / trough1_low <= threshold_pct:\n", "                peak_between = [p for p in peaks if troughs[i] < p < troughs[j]]\n", "                if peak_between:\n", "                    peak_high = df.iloc[peak_between[0]]['High']\n", "                    for k in range(troughs[j], len(df)):\n", "                        if df.iloc[k]['High'] > peak_high:\n", "                            double_bottoms.append({'type': 'bottom', 'key_level': min(trough1_low, trough2_low)})\n", "                            break\n", "                    break\n", "    return double_tops + double_bottoms\n", "\n", "def check_weep(df, key_level, is_above):\n", "    for i in range(len(df)):\n", "        candle = df.iloc[i]\n", "        if is_above:\n", "            if candle['High'] > key_level:\n", "                for j in range(i, len(df)):\n", "                    if df.iloc[j]['Close'] < key_level:\n", "                        return True\n", "                break\n", "        else:\n", "            if candle['Low'] < key_level:\n", "                for j in range(i, len(df)):\n", "                    if df.iloc[j]['Close'] > key_level:\n", "                        return True\n", "                break\n", "    return False\n", "\n", "def is_weep_liquidity(df):\n", "    obs = find_obs(df)\n", "    double_levels = find_double_tops_bottoms(df)\n", "\n", "    wept_count = 0\n", "\n", "    # Check OBs\n", "    for ob in obs:\n", "        start_idx = ob['index'] + 1\n", "        key_level = ob['key_level']\n", "        if ob['type'] == 'bullish':\n", "            is_weep = check_weep(df.iloc[start_idx:], key_level, is_above=False)\n", "        else:\n", "            is_weep = check_weep(df.iloc[start_idx:], key_level, is_above=True)\n", "        if is_weep:\n", "            wept_count += 1\n", "\n", "    # Check double tops/bottoms\n", "    for level in double_levels:\n", "        key_level = level['key_level']\n", "        if level['type'] == 'top':\n", "            is_weep = check_weep(df, key_level, is_above=True)\n", "        else:\n", "            is_weep = check_weep(df, key_level, is_above=False)\n", "        if is_weep:\n", "            wept_count += 1\n", "\n", "    if (wept_count >= 2) or (wept_count >=1 and (len(obs) + len(double_levels)) >=1):\n", "        return [True, wept_count]\n", "    else:\n", "        return [Fals<PERSON>, 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def level_to_waves(level):\n", "    if level['secondary_key_index'] == level['key_level_index']:\n", "        return [{\n", "            'start_index': level['start_index'],\n", "            'checked_index': level['checked_index'],\n", "            'peak_index': level['peak_index'],\n", "            'confirmed': True,\n", "            'label': level['label'],\n", "        }]\n", "    else:\n", "        return [\n", "            {\n", "                'start_index': level['start_index'],\n", "                'checked_index': level['secondary_key_index'],\n", "                'peak_index': level['secondary_key_index'],\n", "                'confirmed': True,\n", "                'label': level['label'],\n", "            },\n", "            {\n", "                'start_index': level['secondary_key_index'],\n", "                'checked_index': level['key_level_index'],\n", "                'peak_index': level['key_level_index'],\n", "                'confirmed': True,\n", "                'label': 1 - level['label'],\n", "            },\n", "            {\n", "                'start_index': level['key_level_index'],\n", "                'checked_index': level['checked_index'],\n", "                'peak_index': level['peak_index'],\n", "                'confirmed': True,\n", "                'label': level['label'],\n", "            }\n", "        ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_combinewave_from_low_tf(start_index, checked_index, waves):\n", "    index = 1\n", "    end_index = 1\n", "    if waves[0]['start_index'] > checked_index:\n", "        return None\n", "    while waves[-index]['start_index'] < start_index:\n", "        index = index + 1\n", "    while waves[-end_index]['checked_index'] > checked_index:\n", "        end_index = end_index + 1\n", "    end_index = 1 - end_index or None\n", "    combine_waves = waves[index:end_index]\n", "    return {\n", "        'start_index': combine_waves[0]['start_index'],\n", "        'checked_index': combine_waves[-1]['checked_index']\n", "    }\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["location_point = {\n", "    'top': 1,\n", "    'bot': 2,\n", "    'prebot': 1,\n", "    'below': 3,\n", "    'mid': 1,\n", "    '68': 2,\n", "    'secondary': 2,\n", "}\n", "\n", "candles_pattern_point = {\n", "    'eg': { 'point': 4, 'reg': is_eg_pair, 'count': 1 },\n", "    'mera': { 'point': 3, 'reg': is_meramera_candles_pair, 'count': 1 },\n", "    'osb': { 'point': 2, 'reg': is_outsidebar_candles_pair, 'count': 1 },\n", "    'nosd': { 'point': 1, 'reg': is_nosd_candles_pair, 'count': 2 },\n", "}\n", "\n", "coc_pattern_point = {\n", "    'correction_broken': {\n", "        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "    },\n", "    'zanshin1': {\n", "        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "    },\n", "    'zanshin2': {\n", "        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'bos': {\n", "        'location': ['bot', '68', 'below', 'prebot'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'break_range_reverse': {\n", "        'location': ['bot', 'below', 'prebot'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'smc': {\n", "        'location': ['68'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'confluent_key': {\n", "        'location': ['68', 'bot', 'prebot'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'trend_reverse': {\n", "        'location': ['68', 'bot', 'prebot'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "        'max_open_trade': 1,\n", "    },\n", "    'main_trend_continue': {\n", "        'location': ['bot', 'below', '68', 'prebot', 'secondary'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': True,\n", "    },\n", "    'range_fake_breakout': {\n", "        'location': ['bot', '68', 'below', 'prebot'],\n", "        'point': 4,\n", "        'entry_now': True,\n", "        'reverse_mode': <PERSON><PERSON><PERSON>,\n", "        'require_ob_tp': False,\n", "        'candle_check': <PERSON><PERSON><PERSON>,\n", "    },\n", "    'is_wave_range_break': 3,\n", "    'is_low_tf_opposite_recent_reverse': 2,\n", "    'is_unconfirm_range_break': 2,\n", "    'is_break_trend_line': 1,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_point(name, main_tf, high_tf, very_high_tf):\n", "    global df, waves, recent_level, primary_level, trend_status\n", "    signal_code = []\n", "    location = location_compare_level(primary_level[name][main_tf])\n", "    coc_pattern, entry_sl_tp = is_coc(name, main_tf, high_tf, very_high_tf)\n", "    # Must have coc pattern to long or short\n", "    if coc_pattern == None:\n", "        return (None,) * 3\n", "    # Location code\n", "    signal_code.append(location)\n", "    # COC signal code\n", "    signal_code.append(coc_pattern)\n", "    # Prepare for pattern candle code\n", "\n", "    if coc_pattern_point[coc_pattern]['candle_check']:\n", "        if waves[name][main_tf][-1]['confirmed'] == False:\n", "            combine_wave = {\n", "                'start_index': waves[name][main_tf][-2]['start_index'],\n", "                'checked_index': waves[name][main_tf][-1]['checked_index'],\n", "                'peak_index': waves[name][main_tf][-2]['peak_index'],\n", "                'confirmed': True,\n", "                'label': waves[name][main_tf][-2]['label']\n", "            }\n", "        else:\n", "            combine_wave = waves[name][main_tf][-1]\n", "        signal_code.append(candle_pattern(df[name][main_tf], combine_wave, primary_level[name][main_tf]['label']))\n", "    else:\n", "        signal_code.append(None)\n", "    return signal_code, total_point_from_signal_code(signal_code), entry_sl_tp\n", "\n", "\n", "def set_default_tp(df, waves, key_level, direction, signal_code, sl):\n", "    tp = df.loc[key_level['peak_index'], 'High' if key_level['label'] else 'Low']\n", "    es_entry = df.loc[waves[-1]['checked_index'], 'Close']\n", "    if signal_code[0] == 'top':\n", "        relate_ob = find_break_candle(df, key_level['start_index'], tp, 1 - key_level['label'])\n", "        if relate_ob:\n", "            relate_wave = find_peak_wave_candle_belongs_to(relate_ob, waves, 1 - key_level['label'])\n", "            tp = df.loc[relate_wave['start_index'], 'Low' if key_level['label'] else 'High']\n", "            if (tp - df.loc[waves[-1]['checked_index'], 'Close'])*direction > 0 and cal_rr(es_entry, sl, tp) >= min_rr:\n", "                return relate_wave['start_index'], tp\n", "    while (tp - df.loc[waves[-1]['checked_index'], 'Close'])*direction <= 0 or cal_rr(es_entry, sl, tp) < min_rr:\n", "        tp = tp + df.loc[key_level['checked_index'], 'ma_candle']*direction*ratio_ma_tp\n", "    return None, tp\n", "\n", "def find_tp(df, waves, key_level, signal_code, old_trend_status, sl):\n", "    if key_level['label'] == 1:\n", "        direction = 1\n", "        low = 'Low'\n", "        high = 'High'\n", "        min_func = min\n", "    else:\n", "        direction = -1\n", "        high = 'Low'\n", "        low = 'High'\n", "        min_func = max\n", "    if (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[key_level['peak_index'], 'Close'])*direction > 0 or\\\n", "        abs(df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[key_level['peak_index'], 'Close']) <= df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:\n", "        return set_default_tp(df, waves, key_level, direction, signal_code, sl)\n", "\n", "    number_waves_after_peak = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)\n", "    if number_waves_after_peak > 0:\n", "        ob_index = None\n", "        sub_level = opposite_level_from_key_level(df, waves, key_level, True)\n", "        if sub_level and sub_level['label'] != key_level['label'] and df.loc[sub_level['key_level_index']][low] - df.loc[waves[-1]['peak_index'], high] > 0:\n", "            if (df.loc[sub_level['key_level_index']][low] - df.loc[waves[-1]['checked_index'], 'Close'])*direction > df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:\n", "                ob_index = sub_level['key_level_index']\n", "        else:\n", "            if (df.loc[key_level['peak_index']][low] - df.loc[waves[-1]['checked_index'], 'Close'])*direction > df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:\n", "                ob_index = key_level['peak_index']\n", "        if ob_index:\n", "            return ob_index, min_func(df.loc[ob_index]['Open'], df.loc[ob_index]['Close']) - direction*df.loc[key_level['checked_index']]['ma_candle']*ratio_ma_tp\n", "    return set_default_tp(df, waves, key_level, direction, signal_code, sl)\n", "\n", "def last_trend_with_same_label(trend_status):\n", "    label = trend_status[0]['label']\n", "    if len(trend_status) == 1:\n", "        return trend_status[0]\n", "    else:\n", "        check_index = -1\n", "        while check_index*-1 <= len(trend_status):\n", "            if trend_status[check_index]['label'] == label and trend_status[check_index]['confirmed']:\n", "                return trend_status[check_index]\n", "            check_index = check_index - 1\n", "\n", "def find_sl(df, waves, key_level, signal_code, old_trend_status, trend_status):\n", "    ob_index = None\n", "    if key_level['label'] == 1:\n", "        low = 'Low'\n", "        high = 'High'\n", "        direction = 1\n", "    else:\n", "        low = 'High'\n", "        high = 'Low'\n", "        direction = -1\n", "    if signal_code[0] in ['below']:\n", "        ob_index = find_closest_ob_below_key_level(df, waves, key_level)\n", "    if signal_code[0] in ['top']:\n", "        ob_index = trend_status[0]['key_level_index']\n", "    if signal_code[0] in ['secondary', 'mid']:\n", "        if len(trend_status) > 1:\n", "            if trend_status[1]['label'] == trend_status[0]['label']:\n", "                ob_index = trend_status[1]['key_level_index']\n", "            else:\n", "                ob_index = key_level.get('ob_correction_index')\n", "    if signal_code[0] in ['68', 'prebot', 'bot']:\n", "        ob_index = key_level['key_level_index']\n", "    if ob_index and (df.loc[ob_index][high] - df.loc[waves[-1]['checked_index']][low])*direction > 0:\n", "        ob_index = None\n", "    if ob_index is None:\n", "        if signal_code[0] == 'below':\n", "            ob_index = key_level.get('ob_correction_index')\n", "        else:\n", "            ob_index = key_level['key_level_index']\n", "    return ob_index\n", "    # if key_level['label'] == 1:\n", "    #     return ob_index, df.loc[ob_index]['Low'] - df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_sl\n", "    # else:\n", "    #     return ob_index, df.loc[ob_index]['High'] + df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_sl\n", "\n", "def find_broken_correction_trend(old_trend_status, trend_status):\n", "    main_trend_label = trend_status[0]['label']\n", "    for index, trend in enumerate(old_trend_status):\n", "        key_list = [d['key_level_index'] for d in trend_status]\n", "        if trend['label'] != main_trend_label and trend['key_level_index'] not in key_list:\n", "            if len(trend_status) > index:\n", "                if trend_status[index]['label'] == main_trend_label:\n", "                    return trend\n", "            else:\n", "                if trend_status[-1]['label'] == main_trend_label:\n", "                    return trend\n", "    return None\n", "\n", "def find_broken_trend(old_trend_status, trend_status):\n", "    for index, trend in enumerate(old_trend_status):\n", "        if index > len(trend_status) - 1 or trend['label'] != trend_status[index]['label']:\n", "            return trend\n", "    return None\n", "\n", "\n", "def find_entry(df, waves, signal_code, old_trend_status, trend_status, sl, tp):\n", "    entry_price = df.loc[waves[-1]['checked_index'], 'Close']\n", "    es_rr = cal_rr(entry_price, sl, tp)\n", "    if coc_pattern_point[signal_code[1]]['entry_now'] or es_rr > min_rr_open_now:\n", "        return (entry_price, sl, tp, es_rr)\n", "    if trend_status[0]['label'] == 1:\n", "        direction = 1\n", "        peak = 'High'\n", "    else:\n", "        direction = -1\n", "        peak = 'Low'\n", "    if (sl - df.loc[trend_status[0]['checked_index'], 'Close'])*direction > 0:\n", "        raise ValueError('sl invalid')\n", "    if (tp - df.loc[trend_status[0]['checked_index'], 'Close'])*direction < 0:\n", "        raise ValueError('tp invalid')\n", "    if len(trend_status) == 1:\n", "        entry_price = df.loc[trend_status[0]['key_level_index'], 'Open']\n", "        es_rr = cal_rr(entry_price, sl, tp)\n", "    else:\n", "        entry_index = find_broken_correction_trend(old_trend_status, trend_status)\n", "        if entry_index is None:\n", "            entry_index =  trend_status[-1]['secondary_key_index']\n", "            entry_price = df.loc[entry_index][peak]\n", "        else:\n", "            entry_index = entry_index['key_level_index']\n", "            entry_price = df.loc[entry_index][peak] + df.loc[trend_status[0]['checked_index']]['ma_candle']*direction\n", "        es_rr = cal_rr(entry_price, sl, tp)\n", "    return (entry_price, sl, tp, es_rr)\n", "\n", "\n", "def cal_rr(entry, sl, tp):\n", "    if entry > tp and entry > sl or entry < tp and entry < sl:\n", "        raise ValueError('Invalid input to cal RR')\n", "    return abs(entry-tp)/abs(entry-sl)\n", "\n", "def find_sl_or_tp_get_hit(name, low_tf, position):\n", "    main_index = position['close_at']\n", "    sl = position['sl']\n", "    tp = position['tp']\n", "    label = position['label']\n", "    low_tf_df = df[name][low_tf].loc[main_index:]\n", "    if label == 1:\n", "        for index, row in low_tf_df.iterrows():\n", "            is_hit_tp = row['High'] >= tp\n", "            is_hit_sl = row['Low'] <= sl\n", "            if is_hit_tp and is_hit_sl:\n", "                return None\n", "            if is_hit_tp:\n", "                return 'tp'\n", "            if is_hit_sl:\n", "                return 'sl'\n", "    else:\n", "        for index, row in low_tf_df.iterrows():\n", "            is_hit_tp = row['Low'] <= tp\n", "            is_hit_sl = row['High'] >= sl\n", "            if is_hit_tp and is_hit_sl:\n", "                return None\n", "            if is_hit_tp:\n", "                return 'tp'\n", "            if is_hit_sl:\n", "                return 'sl'\n", "    print('main index not hit any sl tp')\n", "    return False\n", "\n", "def open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label=None):\n", "    global df, waves, primary_level, trend_status, old_trend_status, fake_breakout_checking, break_index, position_status, pending_position, ratio_before, ratio_after, broken_trendline\n", "    main_df = df[name][main_tf]\n", "    if label is None:\n", "        label = primary_level[name][main_tf]['label']\n", "    if ob_sl is None:\n", "        ob_sl = find_sl(main_df, waves[name][main_tf], primary_level[name][main_tf], signal_code, old_trend_status[name][main_tf], trend_status[name][main_tf])\n", "        tf_sl = main_tf\n", "    if label == 1:\n", "        sl = df[name][tf_sl].loc[ob_sl]['Low'] - df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl\n", "        while sl >= main_df.loc[waves[name][timeframe][-1]['checked_index'], 'Close']:\n", "            sl = sl - df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl\n", "    else:\n", "        sl = df[name][tf_sl].loc[ob_sl]['High'] + df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl\n", "        while sl <= main_df.loc[waves[name][timeframe][-1]['checked_index'], 'Close']:\n", "            sl = sl + df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl\n", "    if ob_tp is None:\n", "        tf_tp = main_tf\n", "        if label == primary_level[name][main_tf]['label']:\n", "            ob_tp, tp = find_tp(main_df, waves[name][main_tf], primary_level[name][main_tf], signal_code, old_trend_status[name][main_tf], sl)\n", "        else:\n", "            # if label == primary_level[name][high_tf]['label']:\n", "            ob_tp, tp = primary_level[name][high_tf]['peak_index'], df[name][high_tf].loc[primary_level[name][high_tf]['peak_index']]['Close']\n", "    else:\n", "        tp = df[name][tf_tp].loc[ob_tp]['Close']\n", "    if entry is None:\n", "        entry, sl, tp, rr = find_entry(main_df, waves[name][main_tf], signal_code, old_trend_status[name][main_tf], trend_status[name][main_tf], sl, tp)\n", "    else:\n", "        rr = cal_rr(entry, sl, tp)\n", "    # if coc_pattern_point[signal_code[1]]['require_ob_tp'] and ob_tp is None:\n", "    #     print('Skip signal because ob tp none')\n", "    #     return\n", "    if rr < min_rr:\n", "        print('Skip signal not reach min rr')\n", "        return\n", "    if signal_code[1] == 'range_fake_breakout':\n", "        fake_breakout_checking = False\n", "        break_index = None\n", "    if coc_pattern_point[signal_code[1]]['entry_now'] or entry == main_df.loc[waves[name][main_tf][-1]['checked_index'], 'Close']:\n", "        init_status = 'open'\n", "    else:\n", "        init_status = 'pending'\n", "    if coc_pattern_point[signal_code[1]]['reverse_mode']:\n", "        t = sl\n", "        sl = tp\n", "        tp = t\n", "        label = 1 - label\n", "        rr = 1/rr\n", "    # Open a position:\n", "    position = {\n", "        'signal_code': signal_code,\n", "        'rr': rr,\n", "        'label': label,\n", "        'tp': tp,\n", "        'sl': sl,\n", "        'ob_sl': ob_sl,\n", "        'tf_sl': tf_sl,\n", "        'ob_tp': ob_tp,\n", "        'tf_tp': tf_tp,\n", "        'entry': entry,\n", "        'entry_at': waves[name][main_tf][-1]['checked_index'],\n", "        'status': init_status,\n", "        'point': point,\n", "        'open_at': None,\n", "        'close_at': None,\n", "        'success': None,\n", "        'key_1_old': old_trend_status[name][timeframe][1]['key_level_index'] if len(old_trend_status[name][timeframe]) > 1 else None,\n", "        'key_1_new': trend_status[name][timeframe][1]['key_level_index'] if len(trend_status[name][timeframe]) > 1 else None,\n", "        'broken_trendline': broken_trendline,\n", "    }\n", "    if init_status == 'open':\n", "        position_status[name][timeframe].append(position)\n", "    else:\n", "        pending_position[name][timeframe].append(position)\n", "    print(f\"New {init_status} position added\")\n", "\n", "def handle_trade(name, main_tf, high_tf, very_high_tf):\n", "    global position_status, primary_level, df, waves, trend_status, closed_position, cancelled_position, pending_position\n", "    current_position = position_status[name][main_tf]\n", "    current_pending = pending_position[name][main_tf]\n", "    latest_checked_index = waves[name][main_tf][-1]['checked_index']\n", "    main_df = df[name][main_tf]\n", "    # No pending or opening position\n", "    # Check signal and point\n", "    signal_code, point, entry_sl_tp = analyze_point(name, main_tf, high_tf, very_high_tf)\n", "    # Condition open position\n", "    if point and coc_pattern_point[signal_code[1]]['max_open_trade'] > len(current_pending) + len(current_position) and point >= min_point_open :\n", "        entry, ob_sl, tf_sl, ob_tp, tf_tp, label = entry_sl_tp if entry_sl_tp else (None,) * 6\n", "        open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label)\n", "    # Check if any pending position to update entry if new signal poin >= the old one\n", "    if current_pending:\n", "        # Fist check pending position has opened?\n", "        if current_pending[-1]['label'] == 1:\n", "            is_open = main_df.loc[latest_checked_index]['Low'] <= current_pending[-1]['entry']\n", "        else:\n", "            is_open = main_df.loc[latest_checked_index]['High'] >= current_pending[-1]['entry']\n", "        if is_open:\n", "            current_pending[-1]['status'] = 'open'\n", "            current_pending[-1]['open_at'] = latest_checked_index\n", "            current_position.append(current_pending.pop())\n", "            print('Pending position has opened')\n", "        else:\n", "            # Cancel pending position if it went too far / hit tp without opening\n", "            is_hit_tp = False\n", "            if current_pending[-1]['label'] == 1:\n", "                is_hit_tp = main_df.loc[latest_checked_index]['High'] >= current_pending[-1]['tp']\n", "            else:\n", "                is_hit_tp = main_df.loc[latest_checked_index]['Low'] <= current_pending[-1]['tp']\n", "            if is_hit_tp and is_confirm_break(main_df, latest_checked_index, current_pending[-1]['tp'], current_pending[-1]['label'], current_pending[-1]['entry_at']):\n", "                print('Pending order cancel due to break tp without opening')\n", "                current_pending[-1]['status'] = 'cancelled'\n", "                cancelled_position[name][main_tf].append(current_pending.pop())\n", "\n", "            # If current pending not open yet but new signal come\n", "            if len(current_pending) > 0:\n", "                if point and point > current_pending[-1]['point']:\n", "                    current_pending.pop()\n", "                    entry, ob_sl, tf_sl, ob_tp, tf_tp, label = entry_sl_tp if entry_sl_tp else (None,) * 6\n", "                    open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label)\n", "\n", "    for position in current_position:\n", "        if position['status'] == 'open':\n", "            # Check position close?\n", "            if position['label'] == 1:\n", "                is_hit_sl = main_df.loc[latest_checked_index]['Low'] <= position['sl']\n", "                is_hit_tp = main_df.loc[latest_checked_index]['High'] >= position['tp']\n", "            else:\n", "                is_hit_sl = main_df.loc[latest_checked_index]['High'] >= position['sl']\n", "                is_hit_tp = main_df.loc[latest_checked_index]['Low'] <= position['tp']\n", "            if is_hit_sl or is_hit_tp:\n", "                position['status'] = 'close'\n", "                position['close_at'] = latest_checked_index\n", "                if position['close_at'] == position['open_at']:\n", "                    # Look into small tf to know hit tp or sl first\n", "                    result = find_sl_or_tp_get_hit(name, low_tf, position) if low_tf else None\n", "                    if result == None or result == False:\n", "                        position['success'] = 'undefined'\n", "                    else:\n", "                        position['success'] = result == 'tp'\n", "                else:\n", "                    position['success'] = is_hit_tp\n", "                closed_position[name][main_tf].append(copy.deepcopy(position))\n", "                current_position.remove(position)\n", "                print(f\"Open position closed with success {is_hit_tp}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_waves_and_level(name, timeframe, key_level, number_waves_before=5, number_waves_after=0):\n", "    global waves\n", "    lines = []\n", "    index_wave_entry = find_wave_index_candle_belongs_to(key_level['secondary_key_index'], waves[name][timeframe])\n", "    index_wave_entry = index_wave_entry - number_waves_before\n", "    if index_wave_entry*-1 >= len(waves[name][timeframe]):\n", "        index_wave_entry = None\n", "    draw_waves = waves[name][timeframe][index_wave_entry:]\n", "    for wave in draw_waves:\n", "        start_date = wave['start_index']\n", "        end_date = wave['peak_index']\n", "        start_close = df[name][timeframe].loc[start_date]['Close']\n", "        end_close = df[name][timeframe].loc[end_date]['Close']\n", "        lines.append([(start_date, start_close), (end_date, end_close)])\n", "\n", "    draw_df = df[name][timeframe].loc[draw_waves[0]['start_index']:draw_waves[-1]['checked_index']]\n", "    if key_level['label'] == 1:\n", "        color_level = (0, 1, 0, 0.1)\n", "    else:\n", "        color_level = (1, 0, 0, 0.1)\n", "\n", "    where_values = df[name][timeframe].loc[key_level['key_level_index']:key_level['checked_index']].index\n", "    where_values = draw_df.index.isin(where_values)\n", "\n", "    where_values_secondary = df[name][timeframe].loc[key_level['secondary_key_index']:key_level['checked_index']].index\n", "    where_values_secondary = draw_df.index.isin(where_values_secondary)\n", "\n", "    fill_color_dic_primary = dict(\n", "        y1=df[name][timeframe].loc[key_level['key_level_index']]['Low'],\n", "        y2=df[name][timeframe].loc[key_level['key_level_index']]['High'],\n", "        color=color_level,\n", "        where=where_values\n", "    )\n", "\n", "    fill_color_dic_secondary = dict(\n", "        y1=df[name][timeframe].loc[key_level['secondary_key_index']]['Low'],\n", "        y2=df[name][timeframe].loc[key_level['secondary_key_index']]['High'],\n", "        color=(1, 1, 0, 0.1),  # Adjust the color as needed\n", "        where=where_values_secondary\n", "    )\n", "\n", "    fill_color_list = [fill_color_dic_primary, fill_color_dic_secondary]\n", "\n", "    if key_level['old_key_level_index']:\n", "        tlines_params = dict(tlines=[(key_level['old_key_level_index'], key_level['key_level_index'])], tline_use='Close', colors='black', linestyle='-.', alpha=0.35)\n", "    else:\n", "        tlines_params = None\n", "\n", "    apds = [\n", "        # mpf.make_addplot(draw_df['tenkan'], color='blue'),\n", "        # mpf.make_addplot(draw_df['kijun'], color='red'),\n", "        # mpf.make_addplot(draw_df['span_a'], color='green', alpha=0.5),\n", "        # mpf.make_addplot(draw_df['span_b'], color='orange', alpha=0.5),\n", "        # mpf.make_addplot(draw_df['chikou'], color='purple'),\n", "    ]\n", "\n", "    # Plot the candlestick chart with waves using 'charles' style\n", "    if tlines_params is not None:\n", "        mpf.plot(draw_df, type='candle',\n", "                addplot=apds,\n", "                alines=dict(alines=lines,\n", "                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1.5]),\n", "                tlines=tlines_params,\n", "                volume=True,\n", "                style='charles',\n", "                title='Candlestick chart with waves',\n", "                fill_between=fill_color_list,\n", "                warn_too_much_data=10000000000,\n", "        )\n", "    else:\n", "        mpf.plot(draw_df, type='candle',\n", "                addplot=apds,\n", "                alines=dict(alines=lines,\n", "                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1.5]),\n", "                volume=True,\n", "                style='charles',\n", "                title='Candlestick chart with waves',\n", "                fill_between=fill_color_list,\n", "                warn_too_much_data=10000000000,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_row_other_tf(name, main_row, other_tf):\n", "    global df\n", "    find_row_other = df[name][other_tf].loc[:main_row.name]\n", "    if len(find_row_other) == 0:\n", "        return None\n", "    return find_row_other.iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def next_row(name, main_row, main_tf, low_tf, high_tf):\n", "    global df\n", "    index = df[name][main_tf].index.get_loc(main_row.name)\n", "    next_main_row =  df[name][main_tf].iloc[index + 1]\n", "    next_low_row = find_row_other_tf(name, next_main_row, low_tf)\n", "    next_high_row = find_row_other_tf(name, next_main_row, high_tf)\n", "    return (next_main_row, next_low_row, next_high_row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def row_to_number_row(name, tf, row):\n", "    if row is None:\n", "        return\n", "    return df[name][tf].index.get_loc(row.name) + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_level_and_status_all(name, main_tf, low_tf, high_tf, number_row):\n", "    global df\n", "    row = update_level_and_status_to_row_number(name, main_tf, number_row)\n", "    row_low = update_level_and_status_to_row_number(name, low_tf, row_to_number_row(name, low_tf, find_row_other_tf(name, row, low_tf)))\n", "    row_high = update_level_and_status_to_row_number(name, high_tf, row_to_number_row(name, high_tf, find_row_other_tf(name, row, high_tf)))\n", "    return (row, row_low, row_high)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_current_position(current_position, name, timeframe, number_waves=10):\n", "    global waves, primary_level, position_status\n", "    local_waves = waves\n", "    key_level = primary_level[name][timeframe]\n", "    if len(current_position) == 0:\n", "        return\n", "\n", "    current_position = current_position[-1]\n", "    lines = []\n", "    present_obs = [current_position['ob_sl'], key_level['key_level_index']]\n", "    if current_position['ob_tp']:\n", "        present_obs.append(current_position['ob_tp'])\n", "    if current_position['broken_trendline']:\n", "        present_obs.append(current_position['broken_trendline']['start_index'])\n", "    index_wave_entry = find_wave_index_candle_belongs_to(min(present_obs), waves[name][timeframe])\n", "    if index_wave_entry != None:\n", "        index_wave_entry = index_wave_entry-number_waves\n", "    index_wave_close = None\n", "    if current_position['close_at']:\n", "        index_wave_close = find_wave_index_candle_belongs_to(current_position['close_at'], local_waves[name][timeframe])\n", "    if index_wave_close and index_wave_close < -1:\n", "        draw_waves = local_waves[name][timeframe][index_wave_entry:index_wave_close+1]\n", "    else:\n", "        if index_wave_entry != None:\n", "            draw_waves = local_waves[name][timeframe][index_wave_entry:]\n", "        else:\n", "            draw_waves = local_waves[name][timeframe]\n", "    for wave in draw_waves:\n", "        start_date = wave['start_index']\n", "        end_date = wave['peak_index']\n", "        start_close = df[name][timeframe].loc[start_date]['Close']\n", "        end_close = df[name][timeframe].loc[end_date]['Close']\n", "        lines.append([(start_date, start_close), (end_date, end_close)])\n", "\n", "    draw_df = df[name][timeframe].loc[draw_waves[0]['start_index']:draw_waves[-1]['checked_index']]\n", "    if key_level['label'] == 1:\n", "        color_level = (0, 1, 0, 0.1)\n", "    else:\n", "        color_level = (1, 0, 0, 0.1)\n", "\n", "    where_values = df[name][timeframe].loc[key_level['key_level_index']:key_level['checked_index']].index\n", "    where_values = draw_df.index.isin(where_values)\n", "\n", "    where_values_secondary = df[name][timeframe].loc[key_level['secondary_key_index']:key_level['checked_index']].index\n", "    where_values_secondary = draw_df.index.isin(where_values_secondary)\n", "\n", "    where_ob_sl = df[name][timeframe].loc[current_position['ob_sl']:key_level['checked_index']].index\n", "\n", "    fill_color_dic_primary = dict(\n", "        y1=df[name][timeframe].loc[key_level['key_level_index']]['Low'],\n", "        y2=df[name][timeframe].loc[key_level['key_level_index']]['High'],\n", "        color=color_level,\n", "        where=where_values\n", "    )\n", "\n", "    fill_color_dic_secondary = dict(\n", "        y1=df[name][timeframe].loc[key_level['secondary_key_index']]['Low'],\n", "        y2=df[name][timeframe].loc[key_level['secondary_key_index']]['High'],\n", "        color=(1, 1, 0, 0.1),  # Adjust the color as needed\n", "        where=where_values_secondary\n", "    )\n", "\n", "    fill_color_dic_ob_sl = dict(\n", "            y1=df[name][current_position['tf_sl']].loc[current_position['ob_sl']]['Open'],\n", "            y2=df[name][current_position['tf_sl']].loc[current_position['ob_sl']]['Close'],\n", "            color='#FFA50080',  # Orrange\n", "            where=draw_df.index.isin(where_ob_sl)\n", "        )\n", "\n", "    fill_color_list = [fill_color_dic_primary, fill_color_dic_secondary, fill_color_dic_ob_sl]\n", "\n", "    if current_position['ob_tp']:\n", "        where_ob_tp = df[name][timeframe].loc[current_position['ob_tp']:key_level['checked_index']].index\n", "\n", "        fill_color_dic_ob_tp = dict(\n", "            y1=df[name][current_position['tf_tp']].loc[current_position['ob_tp']]['Open'],\n", "            y2=df[name][current_position['tf_tp']].loc[current_position['ob_tp']]['Close'],\n", "            color='#00FFFF80',  # <PERSON>an\n", "            where=draw_df.index.isin(where_ob_tp)\n", "        )\n", "        fill_color_list.append(fill_color_dic_ob_tp)\n", "\n", "    # if current_position['key_1_old']:\n", "    #     where_ob_old = df[name][timeframe].loc[current_position['key_1_old']:key_level['checked_index']].index\n", "\n", "    #     fill_color_dic_ob_tp = dict(\n", "    #         y1=df[name][timeframe].loc[current_position['key_1_old']]['Low'],\n", "    #         y2=df[name][timeframe].loc[current_position['key_1_old']]['High'],\n", "    #         color='#AA3FE880',  # Purple\n", "    #         where=draw_df.index.isin(where_ob_old)\n", "    #     )\n", "    #     fill_color_list.append(fill_color_dic_ob_tp)\n", "\n", "    # if current_position['key_1_new']:\n", "    #     where_ob_new = df[name][timeframe].loc[current_position['key_1_new']:key_level['checked_index']].index\n", "\n", "    #     fill_color_dic_ob_tp = dict(\n", "    #         y1=df[name][timeframe].loc[current_position['key_1_new']]['Low'],\n", "    #         y2=df[name][timeframe].loc[current_position['key_1_new']]['High'],\n", "    #         color='#FF45C080',  # Pink\n", "    #         where=draw_df.index.isin(where_ob_new)\n", "    #     )\n", "    #     fill_color_list.append(fill_color_dic_ob_tp)\n", "\n", "    if current_position['broken_trendline']:\n", "        tlines_params = dict(tlines=[(current_position['broken_trendline']['start_index'], current_position['broken_trendline']['end_index'])], tline_use=['Close'], colors='black', linestyle='-.')\n", "    else:\n", "        tlines_params = None\n", "\n", "    # Plot the candlestick chart with waves using 'charles' style\n", "    if tlines_params:\n", "        mpf.plot(draw_df, type='candle',\n", "                alines=dict(alines=lines,\n", "                colors=['b','r','c','k','g'], alpha=[0.35],linewidths=[1]),\n", "                tlines=tlines_params,\n", "                volume=True,\n", "                hlines=dict(hlines=[current_position['tp'],current_position['sl'], current_position['entry']],colors=['g','r','black'], linestyle='-.'),\n", "                vlines=dict(vlines=[current_position['entry_at']], colors=['black'], linestyle='-.'),\n", "                style='charles',\n", "                title='Candlestick chart with waves',\n", "                fill_between=fill_color_list,\n", "                warn_too_much_data=10000000000\n", "        )\n", "    else:\n", "        mpf.plot(draw_df, type='candle',\n", "                alines=dict(alines=lines,\n", "                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1]),\n", "                volume=True,\n", "                hlines=dict(hlines=[current_position['tp'],current_position['sl'], current_position['entry']],colors=['g','r','black'], linestyle='-.'),\n", "                vlines=dict(vlines=[current_position['entry_at']], colors=['black'], linestyle='-.'),\n", "                style='charles',\n", "                title='Candlestick chart with waves',\n", "                fill_between=fill_color_list,\n", "                warn_too_much_data=10000000000\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name = 'BTCUSD'\n", "timeframe = 15\n", "\n", "row = update_level_and_status_to_row_number(name, timeframe, 1)\n", "history = {\n", "    'primary_level': [],\n", "    'sub_primary_level': [],\n", "    'recent_level': [],\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 10, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check drawing level:\n", "row = update_level_and_status_to_row_number(name, timeframe, 500)\n", "plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 10, 0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main_index = df[name][timeframe].index.get_loc(row.name)\n", "row =  df[name][timeframe].iloc[main_index + 1]\n", "update_level_and_trend_status_with_new_row(name, timeframe, row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_history_size = len(history['primary_level'])\n", "while True:\n", "    main_index = df[name][timeframe].index.get_loc(row.name)\n", "    row =  df[name][timeframe].iloc[main_index + 1]\n", "    update_level_and_trend_status_with_new_row(name, timeframe, row)\n", "    if len(history['primary_level']) > old_history_size:\n", "        break\n", "plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 20, 0)\n", "plot_waves_and_level(name, timeframe, history['sub_primary_level'][-1], 20, 0)\n", "plot_waves_and_level(name, timeframe, history['recent_level'][-1], 20, 0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["step = 1000\n", "while step > 0:\n", "    step = step - 1\n", "    main_index = df[name][timeframe].index.get_loc(row.name)\n", "    row =  df[name][timeframe].iloc[main_index + 1]\n", "    update_level_and_trend_status_with_new_row(name, timeframe, row)\n", "\n", "plot_waves_and_level(name, main_tf, primary_level, 10, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["row, row_low, row_high = next_row(name, row, main_tf, high_tf, very_high_tf)\n", "update_level_and_trend_status_with_new_row(name, main_tf, row)\n", "plot_waves_and_level(name, main_tf, primary_level, 10, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["primary_level[name][timeframe]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df[name][timeframe].loc[waves[name][timeframe][-2]['start_index']:waves[name][timeframe][-1]['checked_index']])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_waves_and_level(name, main_tf, recent_level, 10, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check drawing level:\n", "row_very_high = update_level_and_status_to_row_number(name, very_high_tf, 3690, True)\n", "plot_waves_and_level(name, very_high_tf, primary_level, 10, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["waves[name][timeframe][-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recent_level[name][timeframe]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["op_key"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "p_level = level_from_waves(df[name][timeframe], waves[name][timeframe][0:-2], True)\n", "fake_primary_level = {\n", "    name: {\n", "        timeframe: p_level\n", "    }\n", "}\n", "plot_waves_and_level(name, timeframe, fake_primary_level, 3, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_waves_and_level(name, high_tf, primary_level, 5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_waves_and_level(name, timeframe, primary_level, 5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_waves_and_level(name, very_high_tf, primary_level, 30)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}