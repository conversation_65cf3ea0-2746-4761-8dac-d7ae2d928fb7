#!/usr/bin/env python3
"""
Quick start script for Trading Bot
"""

import sys
import os

# Add the trading_bot package to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_backtest():
    """Run a quick backtest with minimal setup"""
    print("Starting Quick Backtest...")
    print("This will test the last 7 days with default settings.")

    try:
        from trading_bot import TradingBot

        # Create bot instance
        bot = TradingBot()

        # Ask user to choose a strategy
        available_strategies = bot.strategy_engine.get_available_strategies()
        print(f"\nAvailable strategies: {', '.join(available_strategies)}")
        strategy_name = input("Enter strategy name: ").strip()

        if strategy_name not in available_strategies:
            print(f"Strategy '{strategy_name}' not found. Please choose from available strategies.")
            return

        print(f"Testing strategy: {strategy_name}")

        # Run quick backtest
        success = bot.run_backtest(
            strategy_filter=strategy_name,
            symbol="BTCUSD",
            rows_back=500,  # Use last 500 1-minute candles for quick test
            initial_balance=10000.0,
            risk_per_trade=0.02
        )

        if success:
            print(f"\n✓ Quick backtest for '{strategy_name}' completed successfully!")
            print("Check the charts/ directory for generated charts.")
            print("Check the backtest_results/ directory for detailed results.")
        else:
            print("\n✗ Quick backtest failed!")
            print("Make sure you have data in the data/ directory.")

    except ImportError as e:
        print(f"Import error: {e}")
        print("Please run setup.py first to install dependencies.")
    except Exception as e:
        print(f"Error: {e}")

def quick_live_demo():
    """Start a quick live demo (connects to Binance)"""
    print("Starting Quick Live Demo...")
    print("This will connect to Binance WebSocket for real-time data.")
    print("Press Ctrl+C to stop.")

    try:
        from trading_bot import TradingBot

        # Create bot instance
        bot = TradingBot()

        # Start live trading demo
        bot.start_live_trading("BTCUSD")

    except ImportError as e:
        print(f"Import error: {e}")
        print("Please run setup.py first to install dependencies.")
    except KeyboardInterrupt:
        print("\nDemo stopped by user.")
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Main quick start menu"""
    print("="*50)
    print("TRADING BOT - QUICK START")
    print("="*50)
    print("Choose a quick start option:")
    print("1. Quick Backtest (7 days)")
    print("2. Live Demo (connects to Binance)")
    print("3. Exit")

    while True:
        try:
            choice = input("\nEnter your choice (1-3): ").strip()

            if choice == '1':
                quick_backtest()
                break
            elif choice == '2':
                quick_live_demo()
                break
            elif choice == '3':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1-3.")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break

if __name__ == "__main__":
    main()
