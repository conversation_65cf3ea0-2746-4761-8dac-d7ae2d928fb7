#!/usr/bin/env python3
"""
Setup script for Trading Bot
"""

import os
import sys
import subprocess

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'charts', 'logs', 'backtest_results']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")
        else:
            print(f"Directory already exists: {directory}")

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        # Install websocket-client if not already installed
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'websocket-client>=1.4.0'])
        print("Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False
    
    return True

def check_dependencies():
    """Check if all required dependencies are available"""
    required_packages = [
        'pandas',
        'numpy', 
        'matplotlib',
        'mplfinance',
        'websocket',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    print("\nAll dependencies are available!")
    return True

def setup_sample_data():
    """Setup sample data for testing"""
    print("Setting up sample data...")
    
    try:
        from trading_bot import BinanceDataFetcher
        
        fetcher = BinanceDataFetcher()
        
        # Fetch sample BTCUSDT data
        print("Fetching sample BTCUSDT data...")
        klines = fetcher.fetch_historical_klines("BTCUSDT", "1m", 1000)
        
        if klines:
            fetcher.save_to_csv(klines, "BTCUSD_1.csv")
            print("Sample data saved successfully!")
        else:
            print("Failed to fetch sample data")
            
    except Exception as e:
        print(f"Error setting up sample data: {e}")
        print("You can manually add CSV files to the data/ directory")

def run_test():
    """Run a simple test to verify installation"""
    print("Running installation test...")
    
    try:
        from trading_bot import TradingBot
        
        # Create bot instance
        bot = TradingBot()
        print("✓ TradingBot imported successfully")
        
        # Test data manager
        from trading_bot import DataManager
        data_manager = DataManager()
        print("✓ DataManager initialized successfully")
        
        # Test strategy engine
        from trading_bot import StrategyEngine
        strategy_engine = StrategyEngine(data_manager)
        print("✓ StrategyEngine initialized successfully")
        
        print("\nInstallation test passed! ✓")
        return True
        
    except Exception as e:
        print(f"Installation test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("="*50)
    print("TRADING BOT SETUP")
    print("="*50)
    
    # Step 1: Create directories
    print("\n1. Creating directories...")
    create_directories()
    
    # Step 2: Check dependencies
    print("\n2. Checking dependencies...")
    if not check_dependencies():
        print("\nInstalling missing dependencies...")
        if not install_dependencies():
            print("Setup failed! Please install dependencies manually.")
            return False
    
    # Step 3: Run test
    print("\n3. Testing installation...")
    if not run_test():
        print("Setup failed! Please check the error messages above.")
        return False
    
    # Step 4: Setup sample data (optional)
    print("\n4. Setting up sample data...")
    response = input("Do you want to fetch sample data from Binance? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        setup_sample_data()
    else:
        print("Skipping sample data setup.")
        print("You can manually add CSV files to the data/ directory.")
    
    print("\n" + "="*50)
    print("SETUP COMPLETED SUCCESSFULLY!")
    print("="*50)
    print("\nNext steps:")
    print("1. Run a backtest: python -m trading_bot.main --mode backtest")
    print("2. Start live trading: python -m trading_bot.main --mode live")
    print("3. Run examples: python example_usage.py")
    print("\nFor more information, see README.md")

if __name__ == "__main__":
    main()
