"""
Example usage of scanwave mode

This script demonstrates how to:
1. Scan wave and level data for a symbol
2. Save the data to a file
3. Load the data later for analysis
"""

import logging
from trading_bot.main import TradingBot
from trading_bot.config import TIMEFRAMES

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def scan_waves_example():
    """Example: Scan waves for a symbol and save to file"""
    logger.info("=== Scanning Waves Example ===")
    
    # Create bot instance in backtest mode (for data loading)
    bot = TradingBot('backtest')
    
    # Scan waves for BTCUSD, last 5000 rows
    symbol = 'BTCUSD'
    rows = 5000
    
    logger.info(f"Scanning waves for {symbol}, last {rows} rows...")
    success = bot.scan_wave(symbol=symbol, rows=rows)
    
    if success:
        logger.info("Wave scan completed successfully!")
        logger.info("Wave and level data has been saved to a file in the data/ directory")
    else:
        logger.error("Wave scan failed")
        return None
    
    return bot


def load_waves_example(filepath: str):
    """Example: Load previously saved wave and level data"""
    logger.info("=== Loading Waves Example ===")
    
    # Create bot instance
    bot = TradingBot('backtest')
    
    # Load wave and level data from file
    logger.info(f"Loading wave and level data from: {filepath}")
    success = bot.load_wave_level_data(filepath)
    
    if success:
        logger.info("Wave and level data loaded successfully!")
        
        # Access the loaded data
        symbol = 'BTCUSD'
        
        # Print summary for each timeframe
        for timeframe in TIMEFRAMES:
            if symbol in bot.data_manager.waves and timeframe in bot.data_manager.waves[symbol]:
                waves = bot.data_manager.waves[symbol][timeframe]
                primary_level = bot.data_manager.primary_level[symbol][timeframe]
                
                logger.info(f"\n{symbol} {timeframe}m:")
                logger.info(f"  Total waves: {len(waves)}")
                
                if primary_level:
                    logger.info(f"  Primary level stack: {primary_level.get('stack_level', 0)}")
                    logger.info(f"  Primary level type: {primary_level.get('type_wave', 'N/A')}")
                    logger.info(f"  Primary level label: {primary_level.get('label', 'N/A')}")
                
                # Show last few waves
                if len(waves) > 0:
                    logger.info(f"  Last wave:")
                    last_wave = waves[-1]
                    logger.info(f"    - Start: {last_wave.get('start_index')}")
                    logger.info(f"    - Peak: {last_wave.get('peak_index')}")
                    logger.info(f"    - Checked: {last_wave.get('checked_index')}")
                    logger.info(f"    - Confirmed: {last_wave.get('confirmed')}")
                    logger.info(f"    - Label: {last_wave.get('label')}")
        
        return bot
    else:
        logger.error("Failed to load wave and level data")
        return None


def main():
    """Main example function"""
    
    # Example 1: Scan waves and save to file
    logger.info("\n" + "="*60)
    logger.info("Example 1: Scanning waves and saving to file")
    logger.info("="*60)
    
    bot = scan_waves_example()
    
    if bot:
        # Example 2: Load the saved data
        # Note: You need to specify the actual filepath that was created
        # The filepath will be printed in the logs from scan_waves_example()
        
        logger.info("\n" + "="*60)
        logger.info("Example 2: Loading saved wave and level data")
        logger.info("="*60)
        logger.info("To load the saved data, use:")
        logger.info("  bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_YYYYMMDD_HHMMSS.pkl')")
        logger.info("\nOr run this script with the filepath as an argument")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # If filepath is provided as argument, load it
        filepath = sys.argv[1]
        load_waves_example(filepath)
    else:
        # Otherwise, run the scan example
        main()

