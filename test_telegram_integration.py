#!/usr/bin/env python3
"""
Test script for Telegram notification integration
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading_bot import TelegramNotifier, TradingBot

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_telegram_notifier():
    """Test TelegramNotifier functionality"""
    print("=" * 50)
    print("Testing Telegram Notifier")
    print("=" * 50)
    
    # Initialize notifier
    notifier = TelegramNotifier()
    
    print(f"Telegram enabled: {notifier.enabled}")
    print(f"Bot token configured: {bool(notifier.bot_token)}")
    print(f"Chat ID configured: {bool(notifier.chat_id)}")
    
    if not notifier.enabled:
        print("\n⚠️  Telegram notifications are disabled.")
        print("To enable, set environment variables:")
        print("export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("export TELEGRAM_CHAT_ID='your_chat_id'")
        return False
    
    # Test connection
    print("\n🔄 Testing connection...")
    if notifier.test_connection():
        print("✅ Connection test successful!")
    else:
        print("❌ Connection test failed!")
        return False
    
    # Test trade opened notification
    print("\n🔄 Testing trade opened notification...")
    sample_trade = {
        'side': 'long',
        'entry_price': 45000.0,
        'stop_loss': 44000.0,
        'take_profit': 47000.0,
        'pattern': 'zanshin2',
        'reason': 'Strong bullish signal'
    }
    
    if notifier.notify_trade_opened("BTCUSD", sample_trade):
        print("✅ Trade opened notification sent!")
    else:
        print("❌ Trade opened notification failed!")
    
    # Test trade closed notification
    print("\n🔄 Testing trade closed notification...")
    sample_closed_trade = {
        'side': 'long',
        'entry_price': 45000.0,
        'exit_price': 46500.0,
        'pattern': 'zanshin2'
    }
    
    if notifier.notify_trade_closed("BTCUSD", sample_closed_trade):
        print("✅ Trade closed notification sent!")
    else:
        print("❌ Trade closed notification failed!")
    
    # Test level change notification
    print("\n🔄 Testing level change notification...")
    if notifier.notify_level_change("BTCUSD", 15, "Primary"):
        print("✅ Level change notification sent!")
    else:
        print("❌ Level change notification failed!")
    
    return True

def test_trading_bot_integration():
    """Test TradingBot integration with Telegram"""
    print("\n" + "=" * 50)
    print("Testing TradingBot Integration")
    print("=" * 50)
    
    try:
        # Initialize bot in live mode
        bot = TradingBot("live")
        
        print(f"✅ TradingBot initialized successfully")
        print(f"Trade mode: {bot.trade_mode}")
        print(f"Telegram notifier available: {hasattr(bot, 'telegram_notifier')}")
        print(f"DataManager has telegram notifier: {hasattr(bot.data_manager, 'telegram_notifier')}")
        print(f"TradeEngine has telegram notifier: {hasattr(bot.trade_engine, 'telegram_notifier')}")
        
        # Test telegram notifier in components
        if hasattr(bot, 'telegram_notifier'):
            print(f"Bot telegram enabled: {bot.telegram_notifier.enabled}")
        
        if hasattr(bot.data_manager, 'telegram_notifier'):
            print(f"DataManager telegram enabled: {bot.data_manager.telegram_notifier.enabled}")
            
        if hasattr(bot.trade_engine, 'telegram_notifier'):
            print(f"TradeEngine telegram enabled: {bot.trade_engine.telegram_notifier.enabled}")
        
        return True
        
    except Exception as e:
        print(f"❌ TradingBot initialization failed: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 Telegram Integration Test Suite")
    print("=" * 50)
    
    # Test 1: TelegramNotifier functionality
    notifier_success = test_telegram_notifier()
    
    # Test 2: TradingBot integration
    integration_success = test_trading_bot_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"TelegramNotifier: {'✅ PASS' if notifier_success else '❌ FAIL'}")
    print(f"TradingBot Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    if notifier_success and integration_success:
        print("\n🎉 All tests passed! Telegram integration is ready.")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
    
    print("\n📝 Next steps:")
    print("1. Set your Telegram bot token and chat ID in environment variables")
    print("2. Test with live trading mode")
    print("3. Monitor notifications during actual trading")

if __name__ == "__main__":
    main()
