"""
Trade engine for both backtesting and live trading
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import pickle
import os
import time


from .config import *
from .data_manager import DataManager
from .strategy_engine import StrategyEngine, StrategyResult
from .chart_generator import ChartGenerator
from .wave_analysis import update_waves_with_new_row_df
from .level_analysis import level_from_waves
from .telegram_notifier import TelegramNotifier

logger = logging.getLogger(__name__)

class BacktestResult:
    """Container for backtest results"""

    def __init__(self):
        self.trades = []
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit = 0.0
        self.max_drawdown = 0.0
        self.win_rate = 0.0
        self.avg_win = 0.0
        self.avg_loss = 0.0
        self.profit_factor = 0.0
        self.sharpe_ratio = 0.0
        self.start_date = None
        self.end_date = None
        self.duration_days = 0
        self.reasons = {}

    def calculate_metrics(self):
        """Calculate performance metrics"""
        try:
            if not self.trades:
                return

            profits = [trade['profit'] for trade in self.trades]

            self.total_trades = len(self.trades)
            self.winning_trades = len([p for p in profits if p > 0])
            self.losing_trades = len([p for p in profits if p < 0])
            self.total_profit = sum(profits)

            # Win rate
            self.win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0

            # Average win/loss
            wins = [p for p in profits if p > 0]
            losses = [p for p in profits if p < 0]

            self.avg_win = np.mean(wins) if wins else 0
            self.avg_loss = np.mean(losses) if losses else 0

            # Profit factor
            total_wins = sum(wins) if wins else 0
            total_losses = abs(sum(losses)) if losses else 0
            self.profit_factor = (total_wins / total_losses) if total_losses > 0 else float('inf')

            # Max drawdown
            cumulative = np.cumsum(profits)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = running_max - cumulative
            self.max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0

            # Sharpe ratio (simplified)
            if len(profits) > 1:
                self.sharpe_ratio = np.mean(profits) / np.std(profits) if np.std(profits) > 0 else 0

            # Duration
            if self.trades:
                dates = [trade['entry_time'] for trade in self.trades]
                self.start_date = min(dates)
                self.end_date = max(dates)
                self.duration_days = (self.end_date - self.start_date).days


            reasons = [trade['reason'] for trade in self.trades]
            reasons_win = [trade['reason'] for trade in self.trades if trade['profit'] > 0]
            reasons_lose = [trade['reason'] for trade in self.trades if trade['profit'] < 0]
            self.reasons = {reason: f"{reasons.count(reason)} ({reasons_win.count(reason)}/{reasons_lose.count(reason)})" for reason in set(reasons)}

        except Exception as e:
            logger.error(f"Error calculating backtest metrics: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'total_profit': self.total_profit,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'sharpe_ratio': self.sharpe_ratio,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'duration_days': self.duration_days,
            'trades': self.trades,
            'reasons': self.reasons
        }

class TradeEngine:
    """Trade engine for both backtesting and live trading"""

    def __init__(self, data_manager: DataManager, strategy_engine: StrategyEngine,
                 trade_mode: str = "backtest", binance_futures_client = None, telegram_notifier: TelegramNotifier = None):
        """
        Initialize TradeEngine

        Args:
            data_manager: DataManager instance
            strategy_engine: StrategyEngine instance
            trade_mode: Trading mode - 'test' for backtesting, 'live' for live trading
            binance_futures_client: Optional BinanceFuturesClient for live trading
            telegram_notifier: Optional TelegramNotifier for notifications
        """
        if trade_mode not in ["backtest", "live"]:
            raise ValueError("trade_mode must be either 'backtest' or 'live'")

        self.data_manager = data_manager
        self.strategy_engine = strategy_engine
        self.trade_mode = trade_mode
        self.results_cache = {}

        # Live trading attributes
        self.open_trades = []
        self.closed_trades = []
        self.live_balance = 10000.0  # Default balance for live trading
        self.risk_per_trade = 0.02  # Default risk per trade

        # Binance Futures API integration
        self.binance_futures_client = binance_futures_client
        self.api_trading_enabled = False
        self.daily_pnl = 0.0  # Track daily profit/loss
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()

        # Telegram notifications
        self.telegram_notifier = telegram_notifier or TelegramNotifier()

    def run_backtest(self, symbol: str, rows_back: int,
                    strategy_filter: str,initial_balance: float = 10000.0, risk_per_trade: float = 0.02) -> BacktestResult:
        """
        Run sequential backtest for a specific strategy

        Args:
            symbol: Trading symbol
            start_date: Start date for backtest
            end_date: End date for backtest
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe
            initial_balance: Starting balance
            risk_per_trade: Risk percentage per trade
            strategy_filter: Strategy name to test (required)

        Returns:
            BacktestResult: Backtest results
        """
        try:

            main_tf = SCANWAVE_TIMEFRAMES[0]
            high_tf = SCANWAVE_TIMEFRAMES[1]
            very_high_tf = SCANWAVE_TIMEFRAMES[2]
            # Get data
            df = self.data_manager.df[symbol][main_tf].tail(rows_back)

            if df is None or len(df) == 0:
                logger.error(f"No data available for {symbol}")
                raise ValueError(f"No data available for {symbol}")

            logger.info(f"Backtesting {len(df)} candles")

            # Run sequential backtest for the specified strategy
            logger.info(f"Running sequential backtest for strategy: {strategy_filter}")
            return self._run_sequential_backtest(
                symbol, df, main_tf, high_tf, very_high_tf,
                initial_balance, risk_per_trade, strategy_filter
            )

        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return BacktestResult()

    def process_candle(self, symbol: str, timestamp: datetime = None, candle_data: Dict = None,
                      strategy_filter: Optional[str] = None) -> Dict:
        """
        Process a new candle for both backtesting and live trading

        Args:
            symbol: Trading symbol
            timestamp: Candle timestamp (for backtest mode)
            candle_data: New candle data (for live mode)
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe
            strategy_filter: Optional strategy name to test (for backtest mode)

        Returns:
            Dict: Processing result with trade information
        """
        try:
            row = candle_data
            main_tf = SCANWAVE_TIMEFRAMES[0]
            high_tf = SCANWAVE_TIMEFRAMES[1]
            very_high_tf = SCANWAVE_TIMEFRAMES[2]

            # Check for trade exits first
            self.open_trades, closed_trades = self._check_trade_exits(self.open_trades, row)

            # Process closed trades
            for trade in closed_trades:
                profit = self._calculate_trade_profit(trade, self.live_balance, self.risk_per_trade)
                self.live_balance += profit
                self.daily_pnl += profit

                trade_record = {
                    'entry_time': trade['entry_time'],
                    'entry_price': trade['entry_price'],
                    'take_profit': trade['take_profit'],
                    'stop_loss': trade['stop_loss'],
                    'exit_time': timestamp,
                    'entry_price': trade['entry_price'],
                    'exit_price': trade['exit_price'],
                    'profit': profit,
                    'pattern': trade['pattern'],
                    'side': trade['side'],
                    'strategy': strategy_filter,
                    'reason': trade['reason']
                }
                self.closed_trades.append(trade_record)

                # Close position on exchange if API trading enabled
                if self.trade_mode == "live" and self.api_trading_enabled and self.binance_futures_client:
                    self._close_exchange_position(symbol, trade)

                # plot
                from .chart_generator import ChartGenerator
                chart_generator = ChartGenerator(self.data_manager)
                chart_path = chart_generator.plot_position_analysis(
                    symbol, main_tf, trade_record, POSITIONS_DIR
                )
                if chart_path:
                    logger.info(f"Chart saved after close trade: {chart_path}")

                # Send Telegram notification for trade closing (only in live mode)
                if self.trade_mode == "live" and self.telegram_notifier:
                    self.telegram_notifier.notify_trade_closed(symbol, trade_record)

            # Check for new trade entries
            if len(self.open_trades) < MAX_CONCURRENT_TRADES_PER_STRATEGY:
                # Check daily limits before opening new trades
                if not self.check_daily_limits():
                    logger.warning("Daily limits exceeded, skipping new trade entry")
                    return {}

                # Test specific strategy
                strategy_result = self.strategy_engine.analyze_specific_strategy(
                    symbol, main_tf, high_tf, very_high_tf, strategy_filter
                )

                if strategy_result and strategy_result.is_valid():
                    new_trade = {
                        'entry_time': timestamp,
                        'entry_price': strategy_result.entry_price,
                        'stop_loss': strategy_result.stop_loss,
                        'take_profit': strategy_result.take_profit,
                        'pattern': strategy_result.pattern_name,
                        'side': 'long' if strategy_result.entry_price < strategy_result.take_profit else 'short',
                        'reason': strategy_result.metadata['reason']
                    }

                    # Execute order on exchange if API trading enabled
                    if self.trade_mode == "live" and self.api_trading_enabled and self.binance_futures_client:
                        order_result = self._execute_exchange_order(symbol, new_trade)
                        if order_result:
                            new_trade['order_id'] = order_result.get('market_order', {}).get('orderId')
                            new_trade['sl_order_id'] = order_result.get('stop_loss_order', {}).get('orderId')
                            new_trade['tp_order_id'] = order_result.get('take_profit_order', {}).get('orderId')
                            new_trade['quantity'] = order_result.get('quantity')
                            logger.info(f"Order executed on exchange: {order_result}")
                        else:
                            logger.error("Failed to execute order on exchange, skipping trade")
                            return {}

                    # plot
                    from .chart_generator import ChartGenerator
                    chart_generator = ChartGenerator(self.data_manager)
                    chart_path = chart_generator.plot_position_analysis(
                        symbol, main_tf, new_trade, POSITIONS_DIR
                    )
                    if chart_path:
                        logger.info(f"Chart saved after signal come: {chart_path}")

                    # Plot also for higher timeframes
                    chart_path = chart_generator.plot_position_analysis(
                        symbol, high_tf, new_trade, POSITIONS_DIR
                    )
                    if chart_path:
                        logger.info(f"Chart saved after signal come high timeframe: {chart_path}")

                    chart_path = chart_generator.plot_position_analysis(
                        symbol, very_high_tf, new_trade, POSITIONS_DIR
                    )
                    if chart_path:
                        logger.info(f"Chart saved after signal come very high timeframe: {chart_path}")

                    self.open_trades.append(new_trade)
                    self.daily_trade_count += 1

                    # Send Telegram notification for trade opening (only in live mode)
                    if self.trade_mode == "live" and self.telegram_notifier:
                        # Use the main timeframe chart for notification
                        main_chart_path = chart_generator.plot_position_analysis(
                            symbol, main_tf, new_trade, POSITIONS_DIR
                        )
                        self.telegram_notifier.notify_trade_opened(symbol, new_trade, main_chart_path)

        except Exception as e:
            logger.error(f"Error processing row timeframe {main_tf}: {timestamp} for strategy {strategy_filter}: {e}")

    def _check_live_trade_exits(self, open_trades: List[Dict], current_price: float) -> Tuple[List[Dict], List[Dict]]:
        """Check if any live trades should be closed based on current price"""
        try:
            remaining_trades = []
            closed_trades = []

            for trade in open_trades:
                should_close = False
                exit_price = current_price

                if trade['side'] == 'long':
                    # Check TP and SL for long trades
                    if current_price >= trade['take_profit']:
                        exit_price = trade['take_profit']
                        should_close = True
                    elif current_price <= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                        should_close = True
                else:
                    # Check TP and SL for short trades
                    if current_price <= trade['take_profit']:
                        exit_price = trade['take_profit']
                        should_close = True
                    elif current_price >= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                        should_close = True

                if should_close:
                    trade['exit_price'] = exit_price
                    trade['exit_time'] = datetime.now()
                    closed_trades.append(trade)
                else:
                    remaining_trades.append(trade)

            return remaining_trades, closed_trades

        except Exception as e:
            logger.error(f"Error checking live trade exits: {e}")
            return open_trades, []

    def get_live_trading_status(self) -> Dict:
        """Get current live trading status"""
        return {
            'trade_mode': self.trade_mode,
            'balance': self.live_balance,
            'open_trades': len(self.open_trades),
            'open_trade_details': self.open_trades.copy(),
            'risk_per_trade': self.risk_per_trade
        }

    def set_live_trading_params(self, balance: float = None, risk_per_trade: float = None):
        """Set live trading parameters"""
        if balance is not None:
            self.live_balance = balance
            logger.info(f"Live trading balance set to: {balance}")

        if risk_per_trade is not None:
            self.risk_per_trade = risk_per_trade
            logger.info(f"Risk per trade set to: {risk_per_trade}")

    def close_all_live_trades(self, current_price: float):
        """Close all open live trades at current market price"""
        if self.trade_mode != "live":
            logger.warning("close_all_live_trades called but trade_mode is not 'live'")
            return

        try:
            for trade in self.open_trades:
                profit = self._calculate_trade_profit(trade, self.live_balance, self.risk_per_trade, current_price)
                self.live_balance += profit

                logger.info(f"Force closed trade: {trade['pattern']} - Profit: {profit:.2f}")

            self.open_trades.clear()
            logger.info(f"All trades closed. Final balance: {self.live_balance:.2f}")

        except Exception as e:
            logger.error(f"Error closing all live trades: {e}")

    def _run_sequential_backtest(self, symbol: str, df: pd.DataFrame, main_tf: int,
                                high_tf: int, very_high_tf: int, initial_balance: float,
                                risk_per_trade: float, strategy_filter: str) -> BacktestResult:
        """Run sequential backtest using _process_chunk_with_strategy for consistency"""
        try:
            logger.info(f"Running sequential backtest for strategy: {strategy_filter}")

            # Use _process_chunk_with_strategy with the entire dataframe
            result = self._process_chunk_with_strategy(
                symbol=symbol,
                chunk=df,
                main_tf=main_tf,
                high_tf=high_tf,
                very_high_tf=very_high_tf,
                initial_balance=initial_balance,
                risk_per_trade=risk_per_trade,
                strategy_name=strategy_filter
            )

            result.calculate_metrics()
            logger.info(f"Sequential backtest completed. Total trades: {result.total_trades}")

            return result

        except Exception as e:
            logger.error(f"Error in sequential backtest: {e}")
            return BacktestResult()


    def _process_chunk_with_strategy(self, symbol: str, chunk: pd.DataFrame, main_tf: int,
                                   high_tf: int, very_high_tf: int, initial_balance: float,
                                   risk_per_trade: float, strategy_name: str) -> BacktestResult:
        """Process data with a specific strategy (sequential processing only)"""
        try:
            # Use existing data manager and strategy engine for sequential processing
            chunk_data_manager = self.data_manager
            chunk_strategy_engine = self.strategy_engine

            result = BacktestResult()
            balance = initial_balance
            open_trades = []

            # Process each candle in the chunk
            for i, (timestamp, row) in enumerate(chunk.iterrows()):
                try:
                    # Update data manager to current timestamp
                    chunk_data_manager.update_data_to_new_timestamp(symbol, timestamp, main_tf)

                    # Update data manager to current timestamp
                    high_df = self.data_manager.df[symbol][high_tf]
                    chunk_data_manager.update_data_to_new_timestamp(symbol, high_df[high_df.index < timestamp].index[-1], high_tf, True)

                    # Check for trade exits first
                    open_trades, closed_trades = self._check_trade_exits(open_trades, row)

                    # Process closed trades
                    for trade in closed_trades:
                        profit = self._calculate_trade_profit(trade, balance, risk_per_trade)
                        balance += profit

                        trade_record = {
                            'id': trade['id'],
                            'entry_time': trade['entry_time'],
                            'entry_price': trade['entry_price'],
                            'take_profit': trade['take_profit'],
                            'stop_loss': trade['stop_loss'],
                            'exit_time': timestamp,
                            'entry_price': trade['entry_price'],
                            'exit_price': trade['exit_price'],
                            'profit': profit,
                            'pattern': trade['pattern'],
                            'side': trade['side'],
                            'strategy': strategy_name,
                            'reason': trade['reason']
                        }
                        result.trades.append(trade_record)

                        # plot
                        from .chart_generator import ChartGenerator
                        chart_generator = ChartGenerator(self.data_manager)
                        chart_path = chart_generator.plot_position_analysis(
                            symbol, 15, trade_record, POSITIONS_DIR
                        )
                        if chart_path:
                            logger.info(f"Chart saved after close trade: {chart_path}")

                    # Check for new trade entries
                    if len(open_trades) < MAX_CONCURRENT_TRADES_PER_STRATEGY:
                        # Test specific strategy
                        strategy_result = chunk_strategy_engine.analyze_specific_strategy(
                            symbol, main_tf, high_tf, very_high_tf, strategy_name
                        )

                        if strategy_result and strategy_result.is_valid():
                            new_trade = {
                                'id': len(result.trades) + 1,
                                'entry_time': timestamp,
                                'entry_price': strategy_result.entry_price,
                                'stop_loss': strategy_result.stop_loss,
                                'take_profit': strategy_result.take_profit,
                                'pattern': strategy_result.pattern_name,
                                'side': 'long' if strategy_result.entry_price < strategy_result.take_profit else 'short',
                                'reason': strategy_result.metadata['reason']
                            }
                            # plot
                            from .chart_generator import ChartGenerator
                            chart_generator = ChartGenerator(self.data_manager)
                            chart_path = chart_generator.plot_position_analysis(
                                symbol, 15, new_trade, POSITIONS_DIR
                            )
                            if chart_path:
                                logger.info(f"Chart saved after signal come: {chart_path}")

                            # Plot also for higher timeframes
                            chart_path = chart_generator.plot_position_analysis(
                                symbol, high_tf, new_trade, POSITIONS_DIR
                            )
                            if chart_path:
                                logger.info(f"Chart saved after signal come high timeframe: {chart_path}")


                            open_trades.append(new_trade)

                    # Progress logging
                    if i % 1000 == 0:
                        logger.info(f"Processed {i}/{len(chunk)} candles, Balance: {balance:.2f}")

                except Exception as e:
                    logger.error(f"Error processing row {i} for strategy {strategy_name}: {e}")
                    continue

            # Close any remaining open trades
            for trade in open_trades:
                profit = self._calculate_trade_profit(trade, balance, risk_per_trade, chunk.iloc[-1]['Close'])
                balance += profit

                trade_record = {
                    'entry_time': trade['entry_time'],
                    'exit_time': chunk.index[-1],
                    'entry_price': trade['entry_price'],
                    'exit_price': chunk.iloc[-1]['Close'],
                    'profit': profit,
                    'pattern': trade['pattern'],
                    'side': trade['side'],
                    'strategy': strategy_name,
                    'reason': trade['reason']
                }
                result.trades.append(trade_record)

            return result

        except Exception as e:
            logger.error(f"Error processing data with strategy {strategy_name}: {e}")
            return BacktestResult()

    def _check_trade_exits(self, open_trades: List[Dict], current_candle: pd.Series) -> Tuple[List[Dict], List[Dict]]:
        """Check if any open trades should be closed"""
        try:
            remaining_trades = []
            closed_trades = []

            for trade in open_trades:
                exit_price = None

                if trade['side'] == 'long':
                    # Check TP and SL for long trades
                    if current_candle['High'] >= trade['take_profit']:
                        exit_price = trade['take_profit']
                    elif current_candle['Low'] <= trade['stop_loss']:
                        exit_price = trade['stop_loss']
                else:
                    # Check TP and SL for short trades
                    if current_candle['Low'] <= trade['take_profit']:
                        exit_price = trade['take_profit']
                    elif current_candle['High'] >= trade['stop_loss']:
                        exit_price = trade['stop_loss']

                if exit_price:
                    trade['exit_price'] = exit_price
                    closed_trades.append(trade)
                else:
                    remaining_trades.append(trade)

            return remaining_trades, closed_trades

        except Exception as e:
            logger.error(f"Error checking trade exits: {e}")
            return open_trades, []

    def _calculate_trade_profit(self, trade: Dict, balance: float, risk_per_trade: float,
                               exit_price: float = None) -> float:
        """Calculate profit for a trade"""
        try:
            if exit_price is None:
                exit_price = trade['exit_price']

            # Calculate position size based on risk
            risk_amount = balance * risk_per_trade
            stop_distance = abs(trade['entry_price'] - trade['stop_loss'])

            if stop_distance == 0:
                return 0

            position_size = risk_amount / stop_distance

            # Calculate profit
            if trade['side'] == 'long':
                profit = (exit_price - trade['entry_price']) * position_size
            else:
                profit = (trade['entry_price'] - exit_price) * position_size

            return profit

        except Exception as e:
            logger.error(f"Error calculating trade profit: {e}")
            return 0

    def save_results(self, result: BacktestResult, filename: str):
        """Save backtest results to file"""
        try:
            os.makedirs('backtest_results', exist_ok=True)
            filepath = os.path.join('backtest_results', filename)

            with open(filepath, 'wb') as f:
                pickle.dump(result.to_dict(), f)

            logger.info(f"Backtest results saved to {filepath}")

        except Exception as e:
            logger.error(f"Error saving backtest results: {e}")

    def load_results(self, filename: str) -> Optional[BacktestResult]:
        """Load backtest results from file"""
        try:
            filepath = os.path.join('backtest_results', filename)

            with open(filepath, 'rb') as f:
                data = pickle.load(f)

            result = BacktestResult()
            result.__dict__.update(data)

            logger.info(f"Backtest results loaded from {filepath}")
            return result

        except Exception as e:
            logger.error(f"Error loading backtest results: {e}")
            return None

    def enable_api_trading(self, leverage: int = None):
        """Enable API trading with Binance Futures"""
        if not self.binance_futures_client:
            logger.error("Cannot enable API trading: BinanceFuturesClient not initialized")
            return False

        try:
            # Test API connection
            account_info = self.binance_futures_client.get_account_info()
            if not account_info:
                logger.error("Failed to connect to Binance Futures API")
                return False

            # Get and set balance
            balance = self.binance_futures_client.get_usdt_balance()
            if balance:
                self.live_balance = balance
                logger.info(f"API Balance: {balance} USDT")

            # Set leverage if specified
            if leverage:
                logger.info(f"Setting leverage to {leverage}x")

            self.api_trading_enabled = True
            logger.info("API trading enabled successfully")
            return True

        except Exception as e:
            logger.error(f"Error enabling API trading: {e}")
            return False

    def disable_api_trading(self):
        """Disable API trading"""
        self.api_trading_enabled = False
        logger.info("API trading disabled")

    def sync_with_exchange(self, symbol: str) -> bool:
        """
        Sync local trades with exchange positions

        Args:
            symbol: Trading symbol to sync

        Returns:
            bool: True if sync successful
        """
        if not self.binance_futures_client:
            return False

        try:
            # Get open positions from exchange
            positions = self.binance_futures_client.get_open_positions()
            if not positions:
                logger.info("No open positions on exchange")
                return True

            # Get open orders
            orders = self.binance_futures_client.get_open_orders(symbol)

            # Update local trades based on exchange state
            symbol_upper = symbol.upper() + 'T' if not symbol.endswith('T') else symbol.upper()

            for position in positions:
                if position['symbol'] == symbol_upper:
                    position_amt = float(position['positionAmt'])
                    if position_amt != 0:
                        # Check if we have this position in local trades
                        found = False
                        for trade in self.open_trades:
                            if trade.get('order_id') == position.get('positionSide'):
                                found = True
                                break

                        if not found:
                            logger.warning(f"Found untracked position on exchange: {position}")

            logger.info(f"Synced with exchange: {len(positions)} positions, {len(orders) if orders else 0} orders")
            return True

        except Exception as e:
            logger.error(f"Error syncing with exchange: {e}")
            return False

    def check_daily_limits(self) -> bool:
        """
        Check if daily trading limits are exceeded

        Returns:
            bool: True if trading is allowed, False if limits exceeded
        """
        try:
            # Reset daily counters if new day
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_pnl = 0.0
                self.daily_trade_count = 0
                self.last_reset_date = current_date
                logger.info("Daily counters reset")

            # Check daily loss limit
            if self.daily_pnl < -MAX_DAILY_LOSS_USDT:
                logger.warning(f"Daily loss limit exceeded: {self.daily_pnl} USDT")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking daily limits: {e}")
            return False

    def _execute_exchange_order(self, symbol: str, trade: Dict) -> Optional[Dict]:
        """
        Execute order on Binance Futures exchange

        Args:
            symbol: Trading symbol
            trade: Trade dictionary with entry_price, stop_loss, take_profit, side

        Returns:
            Dict with order results or None if error
        """
        try:
            if DRY_RUN_MODE:
                logger.info(f"DRY RUN: Would execute order - {trade}")
                return {
                    'market_order': {'orderId': 'DRY_RUN_' + str(int(time.time()))},
                    'stop_loss_order': {'orderId': 'DRY_RUN_SL'},
                    'take_profit_order': {'orderId': 'DRY_RUN_TP'},
                    'quantity': 0.001
                }

            # Convert symbol format (BTCUSD -> BTCUSDT)
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Get current price for calculation
            current_price = self.binance_futures_client.get_current_price(binance_symbol)
            if not current_price:
                logger.error("Failed to get current price")
                return None

            # Calculate position size
            quantity = self.binance_futures_client.calculate_position_size(
                symbol=binance_symbol,
                balance=self.live_balance,
                risk_per_trade=self.risk_per_trade,
                entry_price=current_price,
                stop_loss=trade['stop_loss'],
                leverage=DEFAULT_LEVERAGE
            )

            # Check position size limits
            position_value = quantity * current_price
            if position_value > MAX_POSITION_SIZE_USDT:
                logger.warning(f"Position size {position_value} exceeds max {MAX_POSITION_SIZE_USDT}, adjusting")
                quantity = MAX_POSITION_SIZE_USDT / current_price
            elif position_value < MIN_POSITION_SIZE_USDT:
                logger.warning(f"Position size {position_value} below min {MIN_POSITION_SIZE_USDT}, skipping")
                return None

            # Determine order side
            side = 'BUY' if trade['side'] == 'long' else 'SELL'

            # Place order with SL and TP
            order_result = self.binance_futures_client.place_order_with_sl_tp(
                symbol=binance_symbol,
                side=side,
                quantity=quantity,
                stop_loss=trade['stop_loss'],
                take_profit=trade['take_profit'],
                leverage=DEFAULT_LEVERAGE
            )

            if order_result:
                logger.info(f"Successfully executed order on exchange: {order_result}")
                return order_result
            else:
                logger.error("Failed to execute order on exchange")
                return None

        except Exception as e:
            logger.error(f"Error executing exchange order: {e}")
            return None

    def _close_exchange_position(self, symbol: str, trade: Dict) -> bool:
        """
        Close position on exchange (cancel SL/TP orders)

        Args:
            symbol: Trading symbol
            trade: Trade dictionary with order IDs

        Returns:
            bool: True if successful
        """
        try:
            if DRY_RUN_MODE:
                logger.info(f"DRY RUN: Would close position - {trade}")
                return True

            # Convert symbol format
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Cancel SL and TP orders if they exist
            if trade.get('sl_order_id'):
                try:
                    self.binance_futures_client.cancel_order(binance_symbol, trade['sl_order_id'])
                    logger.info(f"Cancelled SL order: {trade['sl_order_id']}")
                except Exception as e:
                    logger.warning(f"Failed to cancel SL order: {e}")

            if trade.get('tp_order_id'):
                try:
                    self.binance_futures_client.cancel_order(binance_symbol, trade['tp_order_id'])
                    logger.info(f"Cancelled TP order: {trade['tp_order_id']}")
                except Exception as e:
                    logger.warning(f"Failed to cancel TP order: {e}")

            # Note: The position should already be closed by SL or TP trigger
            # If not, we could close it manually here

            return True

        except Exception as e:
            logger.error(f"Error closing exchange position: {e}")
            return False

    def monitor_open_orders(self, symbol: str) -> Dict:
        """
        Monitor status of open orders and update local trades

        Args:
            symbol: Trading symbol

        Returns:
            Dict with monitoring results
        """
        if not self.binance_futures_client or not self.api_trading_enabled:
            return {'status': 'disabled'}

        try:
            # Convert symbol format
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Get open orders from exchange
            open_orders = self.binance_futures_client.get_open_orders(binance_symbol)
            if not open_orders:
                open_orders = []

            # Get open positions
            positions = self.binance_futures_client.get_open_positions()
            if not positions:
                positions = []

            # Check each local trade
            updated_trades = []
            for trade in self.open_trades:
                order_id = trade.get('order_id')
                if not order_id or order_id.startswith('DRY_RUN'):
                    updated_trades.append(trade)
                    continue

                # Check if order is still open
                order_found = any(order['orderId'] == order_id for order in open_orders)

                if not order_found:
                    # Order might be filled or cancelled
                    logger.info(f"Order {order_id} not found in open orders, checking position")

                    # Check if position exists
                    position = next((p for p in positions if p['symbol'] == binance_symbol), None)
                    if position and float(position['positionAmt']) != 0:
                        # Position is open, order was filled
                        trade['status'] = 'filled'
                        logger.info(f"Order {order_id} was filled")
                    else:
                        # No position, order might be cancelled or closed
                        trade['status'] = 'closed'
                        logger.info(f"Order {order_id} appears to be closed")

                updated_trades.append(trade)

            self.open_trades = updated_trades

            return {
                'status': 'success',
                'open_orders_count': len(open_orders),
                'open_positions_count': len(positions),
                'local_trades_count': len(self.open_trades)
            }

        except Exception as e:
            logger.error(f"Error monitoring open orders: {e}")
            return {'status': 'error', 'message': str(e)}

    def emergency_close_all(self, symbol: str) -> bool:
        """
        Emergency function to close all positions and cancel all orders

        Args:
            symbol: Trading symbol

        Returns:
            bool: True if successful
        """
        if not self.binance_futures_client:
            logger.error("Cannot close positions: BinanceFuturesClient not initialized")
            return False

        try:
            logger.warning("EMERGENCY CLOSE ALL INITIATED")

            # Convert symbol format
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Cancel all open orders
            cancel_result = self.binance_futures_client.cancel_all_orders(binance_symbol)
            logger.info(f"Cancelled all orders: {cancel_result}")

            # Close all positions
            close_result = self.binance_futures_client.close_position(binance_symbol)
            logger.info(f"Closed position: {close_result}")

            # Clear local trades
            self.open_trades.clear()

            # Disable API trading
            self.disable_api_trading()

            logger.warning("EMERGENCY CLOSE ALL COMPLETED")
            return True

        except Exception as e:
            logger.error(f"Error in emergency close all: {e}")
            return False
