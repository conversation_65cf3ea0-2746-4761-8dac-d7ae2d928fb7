"""
Technical analysis functions for wave detection and pattern recognition
"""

import pandas as pd
import numpy as np
import copy
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *

logger = logging.getLogger(__name__)

def is_any_candle_outside(check_candles: pd.DataFrame, label: int) -> bool:
    """Check if any candle is outside the first candle's range"""
    first_candle = check_candles.iloc[0]
    other_candles = check_candles.iloc[1:]
    
    if label == 0:  # wave down
        return (other_candles['High'] < first_candle['Low']).any()
    else:  # wave up
        return (other_candles['Low'] > first_candle['High']).any()

def is_candle_complete_inside(first_candle: pd.Series, check_candle: pd.Series) -> bool:
    """Check if candle is completely inside another candle"""
    return (check_candle['High'] <= first_candle['High'] and 
            check_candle['Low'] >= first_candle['Low'])

def is_break_with_space(checked_candles: pd.DataFrame, label: int, break_ratio: float = None) -> bool:
    """Check if break occurs with sufficient space"""
    break_ratio = break_ratio or SPACE_BREAK_RATIO
    
    if label == 1:
        direction = 1
        peak = 'High'
    else:
        direction = -1
        peak = 'Low'
    
    high = checked_candles.iloc[0][peak]
    higher_space = None
    
    for local_index, row in checked_candles.iloc[1:].iterrows():
        if higher_space is not None:
            if (row['Close'] - high) * direction > row['ma_candle'] * break_ratio:
                return True
            else:
                high = higher_space
                higher_space = None
        
        if (row['Close'] - high) * direction > 0:
            higher_space = row['Close']
    
    return False

def is_eg_candle(row: pd.Series) -> bool:
    """Check if candle is an engulfing candle"""
    body_ratio = abs(row['Open'] - row['Close']) * 100.0 / abs(row['High'] - row['Low'])
    return body_ratio >= EG_CANDLE_RATIO and size_candle(row) > row['ma_candle']

def is_doji_candle(row: pd.Series) -> bool:
    """Check if candle is a doji"""
    body_ratio = abs(row['Open'] - row['Close']) * 100.0 / abs(row['High'] - row['Low'])
    return body_ratio <= DOJI_CANDLE_RATIO

def is_big_candle(candle: pd.Series) -> bool:
    """Check if candle is a big candle"""
    return candle['candle_size'] >= candle['ma_candle'] * BIG_CANDLE_RATIO

def size_candle(row: pd.Series) -> float:
    """Get candle size (high - low)"""
    return abs(row['High'] - row['Low'])

def mean_candle(candle: pd.Series) -> float:
    """Get mean price of candle"""
    return (candle['Open'] + candle['Close']) / 2.0

def check_imbalance_3_candles(first_candle: pd.Series, second_candle: pd.Series, 
                             third_candle: pd.Series, label: int) -> bool:
    """Check for imbalance pattern in 3 candles"""
    if not is_eg_candle(second_candle) or not is_big_candle(second_candle):
        return False
    
    if label == 0:
        if (third_candle['Close'] > third_candle['Open'] or 
            second_candle['Close'] > second_candle['Open']):
            return False
        if first_candle['Low'] <= third_candle['High']:
            return False
    else:  # label == 1
        if (third_candle['Close'] < third_candle['Open'] or 
            second_candle['Close'] < second_candle['Open']):
            return False
        if first_candle['High'] >= third_candle['Low']:
            return False
    
    return first_candle.name

def has_imbalance(df: pd.DataFrame, start_index: Any, end_index: Any, label: int) -> Any:
    """Check if there's an imbalance between two points"""
    try:
        i = df.index.get_loc(start_index)
        peak_i = df.index.get_loc(end_index)
        
        if len(df.iloc[i:peak_i+1]) < 3:
            return False
        
        while i + 2 <= peak_i:
            first_candle = df.iloc[i]
            second_candle = df.iloc[i+1]
            third_candle = df.iloc[i+2]
            i += 1
            
            if second_candle['candle_size'] < second_candle['ma_candle'] * MINIMUM_IMBALANCE_RATIO:
                continue
            
            if label == 0:
                if (third_candle['Close'] > third_candle['Open'] or 
                    second_candle['Close'] > second_candle['Open']):
                    continue
                if first_candle['Low'] <= third_candle['High']:
                    continue
            else:  # label == 1
                if (third_candle['Close'] < third_candle['Open'] or 
                    second_candle['Close'] < second_candle['Open']):
                    continue
                if first_candle['High'] >= third_candle['Low']:
                    continue
            
            return first_candle.name
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking imbalance: {e}")
        return False

def is_trend_has_imbalance(df: pd.DataFrame, trend_key: Dict[str, Any]) -> Any:
    """Check if trend has imbalance"""
    return has_imbalance(df, trend_key['key_level_index'], trend_key['peak_index'], trend_key['label'])

def find_break_candle(df: pd.DataFrame, check_index: Any, price: float, label: int, 
                     last: bool = True, start_index: Any = None) -> Optional[Any]:
    """Find the candle that breaks a price level"""
    try:
        if last:
            if label == 1:
                direction = -1
                peak = 'High'
                max_high = df.loc[df.iloc[0].name:check_index]['High'].max()
                if price > max_high:
                    return None
            else:
                direction = 1
                peak = 'Low'
                min_low = df.loc[df.iloc[0].name:check_index]['Low'].min()
                if price < min_low:
                    return None
            
            while True:
                candle = df.loc[check_index]
                if (price >= candle['Open'] and price <= candle['Close']) or \
                   (candle[peak] - price) * direction > 0:
                    return check_index
                
                if df.index.get_loc(check_index) == 0:
                    return None
                
                check_index = df.iloc[df.index.get_loc(check_index) - 1].name
                if check_index < df.iloc[0].name:
                    return None
        else:
            if start_index is None:
                logger.error('Need start index to find first break candle')
                return None
            
            if label == 1:
                max_high = df.loc[start_index:check_index]['Close'].max()
                if price > max_high:
                    return None
                else:
                    local_df = df.loc[start_index:check_index]
                    local_df = local_df.loc[local_df['Close'] > price]
                    return local_df.iloc[0].name if len(local_df) > 0 else None
            else:
                min_low = df.loc[start_index:check_index]['Close'].min()
                if price < min_low:
                    return None
                else:
                    local_df = df.loc[start_index:check_index]
                    local_df = local_df.loc[local_df['Close'] < price]
                    return local_df.iloc[0].name if len(local_df) > 0 else None
                    
    except Exception as e:
        logger.error(f"Error finding break candle: {e}")
        return None

def is_confirm_break(df: pd.DataFrame, row_index: Any, price: float, label: int, 
                    start_index: Any, is_primary: bool = True) -> bool:
    """Check if a break is confirmed"""
    try:
        break_candle_index = find_break_candle(df, row_index, price, label, False, start_index)
        if break_candle_index is None or break_candle_index >= row_index:
            return False
        
        if label == 1:
            low = 'Low'
            high = 'High'
            direction = 1
        else:
            low = 'High'
            high = 'Low'
            direction = -1
        
        # Price going too far so obvious break
        break_candle = df.loc[break_candle_index]
        if (break_candle['Close'] - price) > MAX_CONFIRM_BREAK_RATIO * break_candle['ma_candle']:
            logger.debug('Break confirmed bypass react')
            return True
        
        next_candle_index = df.iloc[df.index.get_loc(break_candle_index) + 1].name
        if next_candle_index > row_index:
            return False
        
        try:
            next_next_candle_index = df.iloc[df.index.get_loc(next_candle_index) + 1].name
        except:
            next_next_candle_index = None
        else:
            if next_next_candle_index > row_index:
                next_next_candle_index = None
        
        next_candle = df.loc[next_candle_index]
        
        # Various confirmation patterns
        if (is_eg_candle(break_candle) and 
            size_candle(next_candle) / size_candle(break_candle) <= SMALL_CANDLE_RATIO and
            (next_candle[low] - price) * direction > 0):
            logger.debug('Break with EG candle and small confirm')
            return True
        
        if (is_eg_candle(break_candle) and is_eg_candle(next_candle) and 
            next_candle['Labels'] == label and
            (next_candle[low] - price) * direction > 0):
            logger.debug('Break with 2 EG candle')
            return True
        
        if (is_big_candle(break_candle) and 
            (next_candle['Close'] - price) * direction > LEVEL_BREAK_SPACE_RATIO * next_candle['ma_candle'] and
            size_candle(next_candle) / size_candle(break_candle) <= SMALL_CANDLE_RATIO):
            logger.debug('Break with big candle and second candle close above and small')
            return True
        
        if is_break_with_space(df.loc[break_candle_index:row_index], label, LEVEL_BREAK_SPACE_RATIO):
            logger.debug('Break with space')
            return True
        
        if (next_next_candle_index and 
            (next_candle[low] - price) * direction > LEVEL_BREAK_SPACE_RATIO * next_candle['ma_candle'] and
            (df.loc[next_next_candle_index][low] - price) * direction > LEVEL_BREAK_SPACE_RATIO * next_candle['ma_candle']):
            logger.debug('Break with candle count')
            return True
        
        fake_price_zone = break_candle[high]
        return is_confirm_break(df, row_index, fake_price_zone, label, next_candle_index, is_primary)
        
    except Exception as e:
        logger.error(f"Error confirming break: {e}")
        return False

def count_waves_from_peak_index(check_index: Any, waves: List[Dict], peak_label: int, 
                               count_unconfirm: bool = False) -> int:
    """Count waves from a peak index"""
    try:
        for index, wave in enumerate(reversed(waves)):
            if check_index >= wave['start_index'] and check_index <= wave['peak_index']:
                result = index
                if waves[-1]['confirmed'] is False and count_unconfirm is False:
                    result = result - 1
                if peak_label != wave['label']:
                    result = result + 1
                if result == -1:
                    return 0
                return result
        return 0
    except Exception as e:
        logger.error(f"Error counting waves: {e}")
        return 0
