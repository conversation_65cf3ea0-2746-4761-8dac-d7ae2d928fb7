"""
Telegram notification system for trading bot
"""

import logging
import requests
import os
from typing import Dict, Any, Optional
from datetime import datetime

from .config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, TELEGRAM_ENABLED

logger = logging.getLogger(__name__)


class TelegramNotifier:
    """Handle Telegram notifications for trading events"""
    
    def __init__(self, bot_token: str = None, chat_id: str = None):
        """
        Initialize Telegram notifier
        
        Args:
            bot_token: Telegram bot token (optional, uses config if not provided)
            chat_id: Telegram chat ID (optional, uses config if not provided)
        """
        self.bot_token = bot_token or TELEGRAM_BOT_TOKEN
        self.chat_id = chat_id or TELEGRAM_CHAT_ID
        self.enabled = bool(self.bot_token and self.chat_id) and TELEGRAM_ENABLED
        
        if not self.enabled:
            logger.warning("Telegram notifications disabled - missing bot token or chat ID")
        else:
            logger.info("Telegram notifications enabled")
            
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
    
    def send_message(self, message: str, parse_mode: str = "Markdown") -> bool:
        """
        Send a text message to Telegram
        
        Args:
            message: Message text to send
            parse_mode: Message formatting (Markdown or HTML)
            
        Returns:
            bool: True if message sent successfully
        """
        if not self.enabled:
            logger.debug(f"Telegram disabled - would send: {message}")
            return False
            
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, data=data, timeout=10)
            response.raise_for_status()
            
            logger.debug(f"Telegram message sent successfully: {message[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    def send_photo(self, photo_path: str, caption: str = "", parse_mode: str = "Markdown") -> bool:
        """
        Send a photo with caption to Telegram
        
        Args:
            photo_path: Path to the image file
            caption: Caption text for the image
            parse_mode: Caption formatting (Markdown or HTML)
            
        Returns:
            bool: True if photo sent successfully
        """
        if not self.enabled:
            logger.debug(f"Telegram disabled - would send photo: {photo_path}")
            return False
            
        if not os.path.exists(photo_path):
            logger.error(f"Photo file not found: {photo_path}")
            return False
            
        try:
            url = f"{self.base_url}/sendPhoto"
            
            with open(photo_path, 'rb') as photo_file:
                files = {'photo': photo_file}
                data = {
                    'chat_id': self.chat_id,
                    'caption': caption,
                    'parse_mode': parse_mode
                }
                
                response = requests.post(url, files=files, data=data, timeout=30)
                response.raise_for_status()
            
            logger.debug(f"Telegram photo sent successfully: {photo_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Telegram photo: {e}")
            return False
    
    def notify_trade_opened(self, symbol: str, trade: Dict[str, Any], chart_path: str = None) -> bool:
        """
        Send notification when a trade is opened
        
        Args:
            symbol: Trading symbol
            trade: Trade information dictionary
            chart_path: Path to position chart image (optional)
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            side_emoji = "🟢" if trade['side'] == 'long' else "🔴"
            
            message = f"""
{side_emoji} *TRADE OPENED*

📊 *Symbol:* {symbol}
📈 *Side:* {trade['side'].upper()}
💰 *Entry Price:* {trade['entry_price']:.4f}
🛑 *Stop Loss:* {trade['stop_loss']:.4f}
🎯 *Take Profit:* {trade['take_profit']:.4f}
📋 *Pattern:* {trade['pattern']}
📝 *Reason:* {trade.get('reason', 'N/A')}
⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # Send message first
            success = self.send_message(message)
            
            # Send chart if available
            if chart_path and os.path.exists(chart_path):
                chart_success = self.send_photo(chart_path, f"📊 Position Chart - {symbol} {trade['side'].upper()}")
                success = success and chart_success
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending trade opened notification: {e}")
            return False
    
    def notify_trade_closed(self, symbol: str, trade: Dict[str, Any]) -> bool:
        """
        Send notification when a trade is closed
        
        Args:
            symbol: Trading symbol
            trade: Trade information dictionary
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            # Calculate profit/loss
            if trade['side'] == 'long':
                pnl = trade['exit_price'] - trade['entry_price']
            else:
                pnl = trade['entry_price'] - trade['exit_price']
            
            pnl_percent = (pnl / trade['entry_price']) * 100
            
            # Determine if win or loss
            is_win = pnl > 0
            result_emoji = "✅" if is_win else "❌"
            
            message = f"""
{result_emoji} *TRADE CLOSED*

📊 *Symbol:* {symbol}
📈 *Side:* {trade['side'].upper()}
💰 *Entry Price:* {trade['entry_price']:.4f}
💸 *Exit Price:* {trade['exit_price']:.4f}
📊 *P&L:* {pnl:.4f} ({pnl_percent:+.2f}%)
📋 *Pattern:* {trade['pattern']}
⏰ *Closed:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending trade closed notification: {e}")
            return False
    
    def notify_level_change(self, symbol: str, timeframe: int, level_type: str = "Primary", chart_path: str = None) -> bool:
        """
        Send notification when a primary level changes
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe in minutes
            level_type: Type of level change (Primary, Secondary, etc.)
            chart_path: Path to level chart image (optional)
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            message = f"""
🔄 *LEVEL CHANGE*

📊 *Symbol:* {symbol}
⏱️ *Timeframe:* {timeframe}m
📈 *Level Type:* {level_type}
⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # Send message first
            success = self.send_message(message)
            
            # Send chart if available
            if chart_path and os.path.exists(chart_path):
                chart_success = self.send_photo(chart_path, f"📊 Level Chart - {symbol} {timeframe}m")
                success = success and chart_success
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending level change notification: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        Test Telegram bot connection
        
        Returns:
            bool: True if connection successful
        """
        if not self.enabled:
            logger.warning("Telegram notifications are disabled")
            return False
            
        test_message = f"🤖 Trading Bot Test Message\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return self.send_message(test_message)
