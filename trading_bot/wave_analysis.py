"""
Wave analysis and level detection functions
"""

import pandas as pd
import numpy as np
import copy
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *
from .technical_analysis import *

logger = logging.getLogger(__name__)

def close_out_body_candle(candle: pd.Series, row_check: pd.Series) -> bool:
    """Check if candle closes outside body of another candle"""
    return ((row_check['Close'] > candle['Open'] and row_check['Close'] > candle['Close']) or
            (row_check['Close'] < candle['Open'] and row_check['Close'] < candle['Close']))

def last_candle_label_same_wave(df: pd.DataFrame, wave: Dict[str, Any]) -> pd.Series:
    """Get last candle with same label as wave"""
    try:
        filtered_df = df.loc[wave['start_index']:wave['checked_index']]
        filtered_df = filtered_df.loc[filtered_df['label'] == wave['label']]
        if len(filtered_df) > 0:
            return filtered_df.iloc[-1]
        return df.loc[wave['start_index']]
    except Exception as e:
        logger.error(f"Error getting last candle same wave: {e}")
        return df.loc[wave['start_index']]

def is_false_confirm_reverse(df: pd.DataFrame, waves: List[Dict], new_row: pd.Series) -> bool:
    """Check if this is a false confirmation reverse"""
    try:
        if len(waves) < 2:
            return False

        if waves[-2]['label']:
            direction = 1
            peak = 'High'
        else:
            direction = -1
            peak = 'Low'

        return (new_row['Close'] - df.loc[waves[-2]['peak_index'], peak]) * direction > 0
    except Exception as e:
        logger.error(f"Error checking false confirm reverse: {e}")
        return False

def update_waves_with_new_row_df(df: pd.DataFrame, waves: List[Dict], new_row_df: pd.Series):
    """Update waves with new row data"""
    try:
        index = df.index.get_loc(new_row_df.name)
        logger.debug(f"New Index call update waves: {index}")

        previous_index = df.index.get_loc(waves[-1]['checked_index'])
        if index <= previous_index:
            logger.debug('This row df already updated')
            return

        # Update Ichimoku indicators
        _update_ichimoku_indicators(df, new_row_df, index)

        # Calculate candle size and MA
        df.loc[new_row_df.name, 'candle_size'] = abs(df.loc[new_row_df.name]['Open'] - df.loc[new_row_df.name]['Close'])

        # Calculate MA candle size
        if index - NUMBER_CAL_MA_CANDLE <= 0:
            df.loc[new_row_df.name, 'ma_candle'] = df.loc[new_row_df.name, 'candle_size']
        else:
            ma_value = df.iloc[index-NUMBER_CAL_MA_CANDLE:index+1]['candle_size'].sum() / NUMBER_CAL_MA_CANDLE
            df.loc[new_row_df.name, 'ma_candle'] = ma_value

        # Check wave direction
        direction = 1 if waves[-1]['label'] else -1
        same_direction = (new_row_df['Close'] - df.loc[waves[-1]['checked_index'], 'Close']) * direction >= 0
        close_in_body = not close_out_body_candle(last_candle_label_same_wave(df, waves[-1]), new_row_df)

        if same_direction or close_in_body:
            _extend_current_wave(df, waves, new_row_df, index, previous_index)
        else:
            _handle_different_direction(df, waves, new_row_df, index, previous_index)

    except Exception as e:
        logger.error(f"Error updating waves: {e}")

def _update_ichimoku_indicators(df: pd.DataFrame, new_row_df: pd.Series, index: int):
    """Update Ichimoku indicators"""
    try:
        last_26 = max(index - 26, 0)

        # Calculate Tenkan-sen (9-period)
        df.loc[new_row_df.name, 'tenkan'] = (
            df.loc[:new_row_df.name].tail(9)['High'].max() +
            df.loc[:new_row_df.name].tail(9)['Low'].min()
        ) / 2.0

        # Calculate Kijun-sen (26-period)
        df.loc[new_row_df.name, 'kijun'] = (
            df.loc[:new_row_df.name].tail(26)['High'].max() +
            df.loc[:new_row_df.name].tail(26)['Low'].min()
        ) / 2.0

        # Calculate Chikou Span
        df.loc[df.iloc[last_26].name, 'chikou'] = df.loc[new_row_df.name]['Close']

        # Calculate Senkou Span A
        df.loc[new_row_df.name, 'span_a'] = (
            df.loc[new_row_df.name]['tenkan'] + df.loc[new_row_df.name]['kijun']
        ) / 2.0

        # Calculate Senkou Span B (52-period)
        df.loc[new_row_df.name, 'span_b'] = (
            df.loc[:new_row_df.name].tail(52)['High'].max() +
            df.loc[:new_row_df.name].tail(52)['Low'].min()
        ) / 2.0

    except Exception as e:
        logger.error(f"Error updating Ichimoku indicators: {e}")

def _extend_current_wave(df: pd.DataFrame, waves: List[Dict], new_row_df: pd.Series,
                        index: int, previous_index: int):
    """Extend current wave with new data"""
    try:
        logger.debug(f"New row df going same direction with current wave label is {new_row_df['Labels']} or close in body previous")

        # Extend wave
        waves[-1]['checked_index'] = new_row_df.name

        # Check new peak current wave
        if waves[-1]['label'] == 1:
            if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                waves[-1]['peak_index'] = new_row_df.name
        else:
            if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                waves[-1]['peak_index'] = new_row_df.name

        # Check confirmation
        if waves[-1]['confirmed'] is False:
            if (waves[-1]['peak_index'] > waves[-2]['peak_index'] and
                is_any_candle_outside(df.loc[waves[-2]['peak_index']:waves[-1]['checked_index']], waves[-1]['label'])):

                # Confirm wave
                waves[-1]['confirmed'] = True
                logger.debug('Current wave now just confirm')

                waves[-1]['ranges'].append({
                    'start_index': waves[-1]['start_index'],
                    'end_index': df.iloc[previous_index].name,
                    'count': index - df.index.get_loc(waves[-1]['start_index'])
                })

                waves[-1]['start_index'] = waves[-2]['peak_index']

                if waves[-1]['label']:
                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['High'].idxmax()
                else:
                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['Low'].idxmin()

                waves[-2]['checked_index'] = waves[-2]['peak_index']
            else:
                # Not confirmed
                logger.debug('Current wave still not confirm, waiting for new row')
                if waves[-2]['label'] == 1:
                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:
                        waves[-2]['peak_index'] = new_row_df.name
                        waves[-2]['checked_index'] = new_row_df.name
                        waves[-1]['start_index'] = new_row_df.name
                else:
                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-2]['peak_index'] = new_row_df.name
                        waves[-2]['checked_index'] = new_row_df.name
                        waves[-1]['start_index'] = new_row_df.name

    except Exception as e:
        logger.error(f"Error extending current wave: {e}")

def _handle_different_direction(df: pd.DataFrame, waves: List[Dict], new_row_df: pd.Series,
                               index: int, previous_index: int):
    """Handle wave going in different direction"""
    try:
        logger.debug('New row direction different with current wave')

        if waves[-1]['confirmed']:
            # Update peak if needed
            if waves[-1]['label'] == 1:
                if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                    waves[-1]['peak_index'] = new_row_df.name
                    waves[-1]['checked_index'] = new_row_df.name
            else:
                if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                    waves[-1]['peak_index'] = new_row_df.name
                    waves[-1]['checked_index'] = new_row_df.name

            logger.debug('Make new unconfirmed wave append')
            # Create new wave
            latest_wave = {
                'start_index': new_row_df.name,
                'checked_index': new_row_df.name,
                'peak_index': new_row_df.name,
                'confirmed': False,
                'label': 1 - waves[-1]['label'],
                'ranges': [],
            }
            waves.append(latest_wave)
        else:
            # Current wave not confirmed
            logger.debug('Current wave was not confirmed then new row different direction occurs')

            if is_false_confirm_reverse(df, waves, new_row_df):
                logger.debug('False confirm reverse break, so extend previous wave to current wave + new row df')

                # Extend previous wave
                waves[-2]['peak_index'] = new_row_df.name
                waves[-2]['checked_index'] = new_row_df.name
                waves[-2]['ranges'].append({
                    'start_index': waves[-1]['start_index'],
                    'end_index': df.iloc[previous_index].name,
                    'count': index - df.index.get_loc(waves[-1]['start_index'])
                })

                waves.pop()

                if len(waves) > 1:
                    if waves[-1]['label']:
                        id_min = df.loc[waves[-1]['start_index']:new_row_df.name, 'Low'].idxmin()
                        if df.loc[waves[-2]['peak_index']]['Low'] > df.loc[id_min]['Low']:
                            waves[-2]['peak_index'] = id_min
                            waves[-2]['checked_index'] = id_min
                            waves[-1]['start_index'] = id_min
                    else:
                        id_max = df.loc[waves[-1]['start_index']:new_row_df.name, 'High'].idxmax()
                        if df.loc[waves[-2]['peak_index']]['High'] < df.loc[id_max]['High']:
                            waves[-2]['peak_index'] = id_max
                            waves[-2]['checked_index'] = id_max
                            waves[-1]['start_index'] = id_max
            else:
                # Update peaks
                if waves[-1]['label'] == 1:
                    if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                        waves[-1]['peak_index'] = new_row_df.name
                else:
                    if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-1]['peak_index'] = new_row_df.name

                if waves[-2]['label'] == 1:
                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:
                        waves[-2]['peak_index'] = new_row_df.name
                else:
                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-2]['peak_index'] = new_row_df.name

            waves[-1]['checked_index'] = new_row_df.name

    except Exception as e:
        logger.error(f"Error handling different direction: {e}")

def find_wave_index_candle_belongs_to(candle_index: Any, waves: List[Dict]) -> Optional[int]:
    """Find which wave index a candle belongs to"""
    check_index = -1
    while True:
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['checked_index'] >= candle_index:
            return check_index
        check_index = check_index - 1
        if check_index*-1 > len(waves):
            return None
        if waves[check_index]['start_index'] < candle_index and waves[check_index]['checked_index'] < candle_index:
            return check_index + 1

def find_peak_wave_candle_belongs_to(candle_index: Any, waves: List[Dict], label: int) -> Optional[Dict]:
    """Find peak wave that candle belongs to"""
    check_index = -1
    while True:
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] >= candle_index and waves[check_index]['label'] == label_wave:
            return waves[check_index]
        check_index = check_index - 1
        if check_index*-1 > len(waves):
            return None
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] <= candle_index and waves[check_index]['label'] == 1 - label_wave:
            return waves[check_index + 1]
