"""
Level analysis and trend detection functions
"""

import pandas as pd
import numpy as np
import copy
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *
from .technical_analysis import *
from .wave_analysis import *

logger = logging.getLogger(__name__)

def cal_ratio_trend(df: pd.DataFrame, check_index: Any, trend_key: Dict[str, Any]) -> float:
    """Calculate trend ratio"""
    try:
        if trend_key['label']:
            direction = 1
            peak = 'High'
            opposite_peak = 'Low'
        else:
            direction = -1
            peak = 'Low'
            opposite_peak = 'High'

        trend_range = abs(df.loc[trend_key['peak_index']][opposite_peak] -
                         df.loc[trend_key['key_level_index']][peak])
        correction_range = abs(df.loc[check_index][peak] -
                              df.loc[trend_key['peak_index']][opposite_peak])

        if trend_range == 0:
            return 0

        return (correction_range / trend_range) * direction

    except Exception as e:
        logger.error(f"Error calculating trend ratio: {e}")
        return 0

def level_from_waves(df: pd.DataFrame, waves: List[Dict], primary_key: bool = False,
                    init_level: Optional[Dict] = None) -> Dict[str, Any]:
    """Generate level from waves"""
    try:
        if len(waves) == 0:
            raise ValueError("waves is empty")

        index = 1

        if init_level:
            recent_level = copy.deepcopy(init_level)
            if recent_level['peak_index'] < waves[0]['peak_index']:
                recent_level['peak_index'] = waves[0]['peak_index']
            if recent_level['checked_index'] < waves[0]['checked_index']:
                recent_level['checked_index'] = waves[0]['checked_index']

            # Filter checked_index
            if waves[-1]['checked_index'] > recent_level['checked_index'] and len(waves) > 4:
                index_latest_wave = find_wave_index_candle_belongs_to(recent_level['checked_index'], waves)
                if index_latest_wave and index_latest_wave > 1 - len(waves):
                    skip_to_index = index_latest_wave + len(waves) - 1
                    if waves[-1]['confirmed'] is False:
                        skip_to_index -= 1
                    index = max(skip_to_index, 1)
        else:
            recent_level = {
                'secondary_key_index': waves[0]['start_index'],
                'key_level_index': waves[0]['start_index'],
                'start_index': waves[0]['start_index'],
                'checked_index': waves[0]['checked_index'],
                'peak_index': waves[0]['peak_index'],
                'label': waves[0]['label'],
                'type_wave': 'wave',
                'stack_level': 0,
                'start_stack_level': 0,
                'false_break_level_index': None,
                'false_break_peak_index': None,
                'old_peak_index': None,
                'old_key_level_index': None,
            }

        len_confirmed_waves = len(waves)
        if waves[-1]['confirmed'] is False:
            len_confirmed_waves -= 1

        while index < len(waves):
            if waves[index]['confirmed'] is False:
                recent_level['checked_index'] = waves[index]['checked_index']
                return recent_level

            if recent_level['label'] == 1:
                peak = 'Low'
                opposite_peak = 'High'
                direction = 1
                compare_func = min
            else:
                peak = 'High'
                opposite_peak = 'Low'
                direction = -1
                compare_func = max

            if waves[index]['label'] != recent_level['label']:
                # Handle opposite direction wave
                if recent_level.get('ob_correction_index') is None:
                    recent_level['ob_correction_index'] = waves[index]['peak_index']
                    recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)
                else:
                    if (df.loc[waves[index]['peak_index'], peak] -
                        df.loc[recent_level['ob_correction_index'], peak]) * direction < 0:
                        recent_level['ob_correction_index'] = waves[index]['peak_index']
                        recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)

                price_check = (df.loc[recent_level['false_break_level_index']][peak]
                              if recent_level['false_break_level_index']
                              else df.loc[recent_level['key_level_index']][peak])

                lookback_index = max(0, df.index.get_loc(waves[index]['checked_index']) - 26)
                displaced_span_b = df.iloc[lookback_index]['span_b']
                price_check = compare_func(price_check, displaced_span_b)

                expanded_checked_index = (waves[index+1]['checked_index']
                                        if index < len(waves) - 1
                                        else waves[index]['checked_index'])

                # Compare with current key level
                if (price_check - df.loc[waves[index]['peak_index']][peak]) * direction > 0:
                    if (waves[index]['confirmed'] and
                        recent_level['key_level_index'] < waves[index]['start_index'] and
                        ((df.loc[recent_level['key_level_index']][peak] -
                          df.loc[waves[index]['start_index']]['Close']) * direction > 0 or
                         is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'],
                                        recent_level['false_break_level_index'] or recent_level['key_level_index'],
                                        primary_key))):

                        number_waves = count_waves_from_peak_index(recent_level['peak_index'],
                                                                  waves[0:index+1], recent_level['label'])

                        if number_waves < 3:
                            recent_level = _create_new_level(recent_level, waves[index])
                        else:
                            if primary_key:
                                recent_level = _handle_primary_key_break(df, recent_level, waves, index)
                            else:
                                recent_level = _create_trend_level(recent_level, waves, index)
                    else:
                        if index < len_confirmed_waves - 1:
                            recent_level['false_break_level_index'] = waves[index]['peak_index']
                        recent_level['checked_index'] = waves[index]['checked_index']
                else:
                    recent_level['checked_index'] = waves[index]['checked_index']
            else:
                # Same direction wave
                price_check = (df.loc[recent_level['false_break_peak_index']][opposite_peak]
                              if recent_level['false_break_peak_index']
                              else df.loc[recent_level['peak_index']][opposite_peak])

                expanded_checked_index = (waves[index+1]['checked_index']
                                        if index < len(waves) - 1
                                        else waves[index]['checked_index'])

                if (df.loc[waves[index]['peak_index']][opposite_peak] - price_check) * direction > 0:
                    if (waves[index]['confirmed'] and
                        recent_level['peak_index'] < waves[index]['start_index'] and
                        ((df.loc[waves[index]['start_index']]['Close'] -
                          df.loc[recent_level['peak_index']][opposite_peak]) * direction > 0 or
                         is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'],
                                        recent_level['false_break_peak_index'] or recent_level['peak_index'],
                                        primary_key))):

                        if primary_key:
                            recent_level = _handle_primary_same_direction(df, recent_level, waves, index)
                        else:
                            recent_level = _create_trend_level_same_direction(recent_level, waves, index)
                    else:
                        if index < len_confirmed_waves - 1:
                            recent_level['false_break_peak_index'] = waves[index]['peak_index']
                        recent_level['checked_index'] = waves[index]['checked_index']
                else:
                    recent_level['checked_index'] = waves[index]['checked_index']

            index += 1

        return recent_level

    except Exception as e:
        logger.error(f"Error generating level from waves: {e}")
        return {}

def _create_new_level(recent_level: Dict, wave: Dict) -> Dict:
    """Create new level structure"""
    return {
        'secondary_key_index': recent_level['key_level_index'],
        'key_level_index': recent_level['peak_index'],
        'start_index': recent_level['key_level_index'],
        'checked_index': wave['checked_index'],
        'peak_index': wave['peak_index'],
        'label': wave['label'],
        'type_wave': 'wave',
        'stack_level': 0,
        'start_stack_level': 0,
        'false_break_level_index': None,
        'false_break_peak_index': None,
        'old_peak_index': None,
        'old_key_level_index': None,
    }

def _create_trend_level(recent_level: Dict, waves: List[Dict], index: int) -> Dict:
    """Create trend level structure"""
    return {
        'secondary_key_index': waves[index-2]['peak_index'],
        'key_level_index': waves[index-1]['peak_index'],
        'start_index': waves[index-2]['peak_index'],
        'checked_index': waves[index]['checked_index'],
        'peak_index': waves[index]['peak_index'],
        'label': waves[index]['label'],
        'type_wave': 'trend',
        'stack_level': 1,
        'start_stack_level': 1,
        'false_break_level_index': None,
        'false_break_peak_index': None,
        'old_peak_index': None,
        'old_key_level_index': None,
    }

def _handle_primary_key_break(df: pd.DataFrame, recent_level: Dict, waves: List[Dict], index: int) -> Dict:
    """Handle primary key break scenario"""
    try:
        if recent_level['label']:
            peak = 'Low'
            start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmax()
        else:
            peak = 'High'
            start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmin()

        new_start_wave_index = find_wave_index_candle_belongs_to(start_index, waves[0:index+1])
        if waves[new_start_wave_index]['label'] == recent_level['label']:
            new_start_wave_index += 1

        init_level = {
            'secondary_key_index': recent_level['key_level_index'],
            'key_level_index': start_index,
            'start_index': start_index,
            'checked_index': waves[new_start_wave_index]['checked_index'],
            'peak_index': waves[new_start_wave_index]['peak_index'],
            'label': waves[new_start_wave_index]['label'],
            'type_wave': 'wave',
            'stack_level': 0,
            'start_stack_level': 0,
            'false_break_level_index': None,
            'false_break_peak_index': None,
            'old_peak_index': None,
            'old_key_level_index': None,
        }

        if len(waves[new_start_wave_index:index+1]) < 2:
            return init_level
        else:
            result = level_from_waves(df, waves[new_start_wave_index:index+1], True, init_level)
            result['start_stack_level'] = result['stack_level']
            return result

    except Exception as e:
        logger.error(f"Error handling primary key break: {e}")
        return recent_level

def _handle_primary_same_direction(df: pd.DataFrame, recent_level: Dict, waves: List[Dict], index: int) -> Dict:
    """Handle primary same direction scenario"""
    try:
        if recent_level['label']:
            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']]['Low'].idxmin()
        else:
            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']]['High'].idxmax()

        return {
            'secondary_key_index': recent_level['peak_index'],
            'key_level_index': key_level,
            'start_index': recent_level['start_index'],
            'checked_index': waves[index]['checked_index'],
            'peak_index': waves[index]['peak_index'],
            'label': waves[index]['label'],
            'type_wave': 'trend',
            'confirmed': True,
            'stack_level': recent_level['stack_level'] + 1,
            'start_stack_level': recent_level['start_stack_level'],
            'false_break_level_index': None,
            'false_break_peak_index': None,
            'old_peak_index': recent_level['peak_index'],
            'old_key_level_index': recent_level['key_level_index'],
        }

    except Exception as e:
        logger.error(f"Error handling primary same direction: {e}")
        return recent_level

def _create_trend_level_same_direction(recent_level: Dict, waves: List[Dict], index: int) -> Dict:
    """Create trend level for same direction"""
    return {
        'secondary_key_index': waves[index-2]['peak_index'],
        'key_level_index': waves[index-1]['peak_index'],
        'start_index': recent_level['start_index'],
        'checked_index': waves[index]['checked_index'],
        'peak_index': waves[index]['peak_index'],
        'label': waves[index]['label'],
        'type_wave': 'trend',
        'confirmed': True,
        'stack_level': recent_level['stack_level'] + 1,
        'start_stack_level': recent_level['start_stack_level'],
        'false_break_level_index': None,
        'false_break_peak_index': None,
        'old_peak_index': recent_level['peak_index'],
        'old_key_level_index': recent_level['key_level_index'],
    }

def opposite_level_from_key_level(df: pd.DataFrame, waves: List[Dict], key_level: Dict, is_primary: bool) -> Optional[Dict]:
    """Get opposite level from key level"""
    try:
        number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)
        if number_waves == 0:
            return None
        if number_waves == 1:
            return level_from_waves(df, [waves[-1]], is_primary)
        return level_from_waves(df, waves[-number_waves:], is_primary)
    except Exception as e:
        logger.error(f"Error getting opposite level: {e}")
        return None

def location_compare_level(key_level: Dict) -> str:
    """Compare level location"""
    try:
        if key_level.get('ob_correction_index') is None:
            return 'top'

        ratio = key_level.get('ratio_trend', 0)

        if ratio <= -0.8:
            return 'below'
        elif ratio <= -0.68:
            return '68'
        elif ratio <= -0.5:
            return 'prebot'
        elif ratio <= -0.2:
            return 'bot'
        elif ratio <= 0.2:
            return 'mid'
        else:
            return 'secondary'

    except Exception as e:
        logger.error(f"Error comparing level location: {e}")
        return 'mid'
