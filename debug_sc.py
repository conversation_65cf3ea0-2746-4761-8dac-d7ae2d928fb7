import logging
import time
import signal
import sys
from datetime import datetime
from typing import Dict

from trading_bot.config import *
from trading_bot.data_manager import DataManager
from trading_bot.strategy_engine import StrategyEngine
from trading_bot.chart_generator import ChartGenerator
from trading_bot.binance_client import BinanceWebSocketClient, BinanceDataFetcher, create_binance_hook
from trading_bot.trade_engine import TradeEngine
from trading_bot.main import TradingBot

bot = TradingBot()
name = 'BTCUSD'
main_tf = 15
high_tf = 60
very_high_tf = 1440

# Initialize data
if not bot.initialize_data('BTCUSD'):
    print("Failed to initialize data")


import pandas as pd

current_time = bot.data_manager.waves[name][main_tf][-1]['checked_index']
history = bot.data_manager.history[name][main_tf]['primary_level']
latest_time = bot.data_manager.get_latest_candle(name, main_tf).name

# Update data to next timeframe
while True:
  len_history = len(history)
  next_time = current_time + pd.Timedelta(minutes=main_tf)
  if next_time > latest_time:
    break
  bot.data_manager.update_data_to_new_timestamp(name, next_time, main_tf)
  current_time = next_time

