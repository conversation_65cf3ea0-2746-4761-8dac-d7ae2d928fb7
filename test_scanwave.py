"""
Test script for scanwave mode functionality
"""

import logging
import os
import sys
from trading_bot.main import TradingBot
from trading_bot.config import TIMEFRAMES

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_scanwave():
    """Test the scanwave functionality"""
    logger.info("="*60)
    logger.info("Testing Scanwave Mode")
    logger.info("="*60)
    
    try:
        # Create bot instance
        logger.info("\n1. Creating TradingBot instance...")
        bot = TradingBot('backtest')
        logger.info("   ✓ Bot created successfully")
        
        # Test scan_wave
        logger.info("\n2. Scanning waves for BTCUSD (last 1000 rows)...")
        symbol = 'BTCUSD'
        rows = 1000
        
        success = bot.scan_wave(symbol=symbol, rows=rows)
        
        if not success:
            logger.error("   ✗ Wave scan failed")
            return False
        
        logger.info("   ✓ Wave scan completed successfully")
        
        # Verify data was saved
        logger.info("\n3. Verifying saved data...")
        data_dir = 'data'
        files = [f for f in os.listdir(data_dir) if f.startswith(f'{symbol}_wave_level_')]
        
        if not files:
            logger.error("   ✗ No wave level files found")
            return False
        
        # Get the most recent file
        latest_file = sorted(files)[-1]
        filepath = os.path.join(data_dir, latest_file)
        logger.info(f"   ✓ Found saved file: {latest_file}")
        
        # Test loading the data
        logger.info("\n4. Testing load_wave_level_data...")
        bot2 = TradingBot('backtest')
        
        load_success = bot2.load_wave_level_data(filepath)
        
        if not load_success:
            logger.error("   ✗ Failed to load wave level data")
            return False
        
        logger.info("   ✓ Wave level data loaded successfully")
        
        # Verify loaded data
        logger.info("\n5. Verifying loaded data structure...")
        
        if symbol not in bot2.data_manager.waves:
            logger.error(f"   ✗ Symbol {symbol} not found in waves data")
            return False
        
        logger.info(f"   ✓ Symbol {symbol} found in loaded data")
        
        # Check each timeframe
        logger.info("\n6. Checking data for each timeframe...")
        for timeframe in TIMEFRAMES:
            if timeframe not in bot2.data_manager.waves[symbol]:
                logger.warning(f"   ⚠ Timeframe {timeframe}m not found")
                continue
            
            waves = bot2.data_manager.waves[symbol][timeframe]
            primary_level = bot2.data_manager.primary_level[symbol][timeframe]
            recent_level = bot2.data_manager.recent_level[symbol][timeframe]
            
            logger.info(f"\n   {timeframe}m Timeframe:")
            logger.info(f"     - Waves: {len(waves)}")
            
            if primary_level:
                logger.info(f"     - Primary level stack: {primary_level.get('stack_level', 0)}")
                logger.info(f"     - Primary level type: {primary_level.get('type_wave', 'N/A')}")
                logger.info(f"     - Primary level label: {'Bullish' if primary_level.get('label') == 1 else 'Bearish'}")
            else:
                logger.warning(f"     - No primary level data")
            
            if recent_level:
                logger.info(f"     - Recent level available: Yes")
            else:
                logger.info(f"     - Recent level available: No")
            
            # Check last wave
            if len(waves) > 0:
                last_wave = waves[-1]
                logger.info(f"     - Last wave confirmed: {last_wave.get('confirmed', False)}")
                logger.info(f"     - Last wave label: {'Bullish' if last_wave.get('label') == 1 else 'Bearish'}")
        
        logger.info("\n" + "="*60)
        logger.info("✓ All tests passed successfully!")
        logger.info("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_consistency():
    """Test that loaded data matches original data"""
    logger.info("\n" + "="*60)
    logger.info("Testing Data Consistency")
    logger.info("="*60)
    
    try:
        # Scan and save
        logger.info("\n1. Scanning and saving wave data...")
        bot1 = TradingBot('backtest')
        bot1.scan_wave(symbol='BTCUSD', rows=500)
        
        # Get the saved file
        data_dir = 'data'
        files = [f for f in os.listdir(data_dir) if f.startswith('BTCUSD_wave_level_')]
        latest_file = sorted(files)[-1]
        filepath = os.path.join(data_dir, latest_file)
        
        # Load the data
        logger.info("\n2. Loading saved wave data...")
        bot2 = TradingBot('backtest')
        bot2.load_wave_level_data(filepath)
        
        # Compare data
        logger.info("\n3. Comparing original and loaded data...")
        symbol = 'BTCUSD'
        
        for timeframe in TIMEFRAMES:
            waves1 = bot1.data_manager.waves[symbol][timeframe]
            waves2 = bot2.data_manager.waves[symbol][timeframe]
            
            if len(waves1) != len(waves2):
                logger.error(f"   ✗ Wave count mismatch for {timeframe}m: {len(waves1)} vs {len(waves2)}")
                return False
            
            logger.info(f"   ✓ {timeframe}m: {len(waves1)} waves match")
        
        logger.info("\n✓ Data consistency test passed!")
        return True
        
    except Exception as e:
        logger.error(f"\n✗ Consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Run tests
    test1_passed = test_scanwave()
    
    if test1_passed:
        test2_passed = test_data_consistency()
        
        if test2_passed:
            logger.info("\n" + "="*60)
            logger.info("ALL TESTS PASSED! ✓")
            logger.info("="*60)
            sys.exit(0)
        else:
            logger.error("\nConsistency test failed")
            sys.exit(1)
    else:
        logger.error("\nScanwave test failed")
        sys.exit(1)

