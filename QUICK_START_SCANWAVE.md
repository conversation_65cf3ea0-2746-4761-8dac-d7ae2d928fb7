# Quick Start Guide - Scanwave Mode

## What is Scanwave Mode?

Scanwave mode analyzes price data to identify wave patterns and support/resistance levels, then saves the results to a file. This allows you to:
- Pre-compute analysis once and reuse it multiple times
- Save analysis snapshots for later review
- Speed up backtesting by loading pre-computed data

## 5-Minute Quick Start

### Step 1: Scan Waves

Run this command to analyze the last 5000 candles of BTCUSD:

```bash
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 5000
```

**What happens:**
- Loads 5000 rows of 1-minute data
- Creates 5m, 15m, 1h, and 1d timeframes
- Calculates wave patterns for each timeframe
- Saves results to `data/BTCUSD_wave_level_backtest_YYYYMMDD_HHMMSS.pkl`

**Expected output:**
```
2024-10-16 14:30:22 - INFO - Starting wave scan for BTCUSD, last 5000 rows
2024-10-16 14:30:23 - INFO - Updating wave and level data for all timeframes...
2024-10-16 14:30:24 - INFO - Processing BTCUSD 1m - 5000 candles
2024-10-16 14:30:25 - INFO -   Waves: 234, Primary level stack: 3
2024-10-16 14:30:26 - INFO - Processing BTCUSD 5m - 1000 candles
2024-10-16 14:30:27 - INFO -   Waves: 89, Primary level stack: 2
...
2024-10-16 14:30:35 - INFO - Wave and level data saved to: data/BTCUSD_wave_level_backtest_20241016_143022.pkl
2024-10-16 14:30:35 - INFO - Wave scan completed successfully
```

### Step 2: Load and Use the Data

Create a Python script or use the example:

```python
from trading_bot import TradingBot

# Create bot instance
bot = TradingBot('backtest')

# Load the saved data (use the actual filename from Step 1)
bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')

# Access the data
symbol = 'BTCUSD'
timeframe = 15  # 15-minute timeframe

# Get waves
waves = bot.data_manager.waves[symbol][timeframe]
print(f"Total waves: {len(waves)}")

# Get primary level
primary_level = bot.data_manager.primary_level[symbol][timeframe]
print(f"Stack level: {primary_level.get('stack_level')}")
print(f"Type: {primary_level.get('type_wave')}")

# Get last wave
if len(waves) > 0:
    last_wave = waves[-1]
    print(f"Last wave confirmed: {last_wave['confirmed']}")
    print(f"Last wave label: {'Bullish' if last_wave['label'] == 1 else 'Bearish'}")
```

### Step 3: Run the Example Script

```bash
# Scan and save
python example_scanwave.py

# Load and analyze (use actual filename)
python example_scanwave.py data/BTCUSD_wave_level_backtest_20241016_143022.pkl
```

## Common Use Cases

### Use Case 1: Pre-compute for Backtesting

```bash
# Scan once
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 10000

# Then run multiple backtests using the pre-computed data
# (Future enhancement - currently backtests recalculate)
```

### Use Case 2: Analyze Wave Patterns

```python
from trading_bot import TradingBot

bot = TradingBot('backtest')
bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')

# Analyze all timeframes
for tf in [1, 5, 15, 60, 1440]:
    waves = bot.data_manager.waves['BTCUSD'][tf]
    primary = bot.data_manager.primary_level['BTCUSD'][tf]
    
    print(f"\n{tf}m Timeframe:")
    print(f"  Waves: {len(waves)}")
    print(f"  Stack Level: {primary.get('stack_level')}")
    
    # Count confirmed waves
    confirmed = sum(1 for w in waves if w['confirmed'])
    print(f"  Confirmed: {confirmed}/{len(waves)}")
```

### Use Case 3: Debug Wave Detection

```python
from trading_bot import TradingBot

bot = TradingBot('backtest')
bot.load_wave_level_data('data/BTCUSD_wave_level_backtest_20241016_143022.pkl')

# Examine last 5 waves on 15m timeframe
waves = bot.data_manager.waves['BTCUSD'][15]

for i, wave in enumerate(waves[-5:], 1):
    print(f"\nWave {i}:")
    print(f"  Start: {wave['start_index']}")
    print(f"  Peak: {wave['peak_index']}")
    print(f"  Confirmed: {wave['confirmed']}")
    print(f"  Direction: {'Up' if wave['label'] == 1 else 'Down'}")
```

## Command-Line Options

```bash
python -m trading_bot.main --mode scanwave [OPTIONS]

Options:
  --symbol SYMBOL    Trading symbol (default: BTCUSD)
  --rows ROWS        Number of 1-minute rows to scan (default: 1000000)

Examples:
  # Scan 5000 rows
  python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 5000
  
  # Scan 10000 rows
  python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 10000
  
  # Different symbol
  python -m trading_bot.main --mode scanwave --symbol ETHUSD --rows 5000
```

## Output Files

Files are saved in the `data/` directory with this naming pattern:

```
{SYMBOL}_wave_level_{MODE}_{TIMESTAMP}.pkl
```

Examples:
- `BTCUSD_wave_level_backtest_20241016_143022.pkl`
- `ETHUSD_wave_level_backtest_20241016_150530.pkl`

## What's Saved?

For each timeframe (1m, 5m, 15m, 1h, 1d), the file contains:

1. **Waves** - List of wave patterns with:
   - Start, peak, and checked timestamps
   - Confirmation status
   - Direction (bullish/bearish)
   - Ranges

2. **Primary Level** - Main support/resistance level with:
   - Key level timestamps
   - Stack level (trend strength)
   - Type (wave/trend)
   - Direction

3. **Recent Level** - Most recent level data

4. **Sub-Primary Level** - Secondary level data

5. **History** - Historical level changes

## Performance Tips

- **Start small**: Begin with 1000-5000 rows to test
- **Memory**: Larger datasets (>10000 rows) use more memory
- **Time**: Processing time increases with row count
- **Storage**: Pickle files are typically 1-10 MB

## Troubleshooting

### Problem: "Failed to initialize data"
**Solution**: Make sure CSV data files exist in `data/` directory
```bash
ls data/BTCUSD_1_backtest.csv
```

### Problem: "File not found" when loading
**Solution**: Check the filename and path
```bash
ls data/BTCUSD_wave_level_*.pkl
```

### Problem: Out of memory
**Solution**: Reduce the number of rows
```bash
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 1000
```

### Problem: No waves detected
**Solution**: Check if data file has enough rows
```bash
wc -l data/BTCUSD_1_backtest.csv
```

## Testing

Run the test script to verify everything works:

```bash
python test_scanwave.py
```

Expected output:
```
============================================================
Testing Scanwave Mode
============================================================

1. Creating TradingBot instance...
   ✓ Bot created successfully

2. Scanning waves for BTCUSD (last 1000 rows)...
   ✓ Wave scan completed successfully

3. Verifying saved data...
   ✓ Found saved file: BTCUSD_wave_level_backtest_20241016_143022.pkl

4. Testing load_wave_level_data...
   ✓ Wave level data loaded successfully

5. Verifying loaded data structure...
   ✓ Symbol BTCUSD found in loaded data

6. Checking data for each timeframe...
   [Details for each timeframe...]

============================================================
✓ All tests passed successfully!
============================================================
```

## Next Steps

1. **Read the full documentation**: See `SCANWAVE_FEATURE.md`
2. **Try the examples**: Run `example_scanwave.py`
3. **Experiment**: Try different symbols and row counts
4. **Integrate**: Use loaded data in your analysis scripts

## Getting Help

- Check `SCANWAVE_FEATURE.md` for detailed documentation
- Review `example_scanwave.py` for code examples
- Run `test_scanwave.py` to verify your setup
- Check logs in `logs/trading_bot.log` for errors

## Summary

```bash
# 1. Scan and save
python -m trading_bot.main --mode scanwave --symbol BTCUSD --rows 5000

# 2. Load and use
python example_scanwave.py data/BTCUSD_wave_level_backtest_YYYYMMDD_HHMMSS.pkl

# 3. Test
python test_scanwave.py
```

That's it! You're ready to use scanwave mode. 🚀

