#!/usr/bin/env python3
"""
Test script to verify SSL fix for Binance WebSocket connection
"""

import sys
import time
import logging
from trading_bot.binance_client import BinanceWebSocketClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_callback(candle_data):
    """Test callback function for WebSocket data"""
    symbol = candle_data.get('symbol', 'Unknown')
    close_price = candle_data.get('Close', 0)
    timestamp = candle_data.get('Close time', 'Unknown')

    logger.info(f"Received candle data for {symbol}: Close={close_price}, Time={timestamp}")

def main():
    """Test the SSL fix"""
    logger.info("Testing Binance WebSocket SSL fix...")

    try:
        # Create WebSocket client
        ws_client = BinanceWebSocketClient(test_callback)

        # Try to connect
        logger.info("Attempting to connect to Binance WebSocket...")
        success = ws_client.connect("BTCUSDT")

        if success:
            logger.info("Connection successful! Waiting for data...")

            # Wait for some data
            start_time = time.time()
            timeout = 30  # 30 seconds timeout

            # Give it a moment to establish connection
            time.sleep(2)

            while time.time() - start_time < timeout:
                if ws_client.is_alive():
                    logger.info(f"WebSocket is alive and connected. Status: {ws_client.is_connected}")
                    time.sleep(5)
                else:
                    logger.error(f"WebSocket connection lost. Status: {ws_client.is_connected}")
                    break

            logger.info("Test completed. Disconnecting...")
            ws_client.disconnect()

        else:
            logger.error("Failed to connect to WebSocket")
            return False

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        if 'ws_client' in locals():
            ws_client.disconnect()
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False

    logger.info("SSL fix test completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
